<script setup lang="ts">
const testimonials = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    username: '<PERSON>te<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTl8fHVzZXJ8ZW58MHx8MHx8fDA%3D',
    text: 'M<PERSON>t hành trình đáng nhớ với những đồng nghiệp tuyệt vời. Cam on vi da den!',
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    username: 'Backend Developer',
    avatar: 'https://images.unsplash.com/photo-1557862921-37829c790f19?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjN8fHVzZXJ8ZW58MHx8MHx8fDA%3D',
    text: 'Mỗi ngày làm việc ở đây đều là một trải nghiệm quý giá. Cảm ơn vì những kỷ niệm đẹp và tình cảm chân thành!',
  },
  {
    id: 3,
    name: 'Phạm Huỳnh Thiên Ân',
    username: 'Careteam',
    avatar: 'https://images.unsplash.com/photo-1605993439219-9d09d2020fa5?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MzF8fHVzZXJ8ZW58MHx8MHx8fDA%3D',
    text: 'Chúng ta đã cùng nhau tạo nên một môi trường tràn đầy cảm hứng và sự gắn kết. Sẽ luôn nhớ về nơi này!',
  },
  {
    id: 4,
    name: 'Nguyễn Cảnh Đức',
    username: 'Frontend Developer',
    avatar: 'https://plus.unsplash.com/premium_photo-1689606093808-3cb4393248d2?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mjl8fHVzZXJ8ZW58MHx8MHx8fDA%3D',
    text: 'Dù hành trình này khép lại, nhưng những ký ức, tình bạn và bài học quý giá sẽ luôn còn mãi.',
  },
  {
    id: 5,
    name: 'Nguyễn Kim Hưng',
    username: 'Frontend Developer',
    avatar: 'https://images.unsplash.com/photo-1624561172888-ac93c696e10c?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MzJ8fHVzZXJ8ZW58MHx8MHx8fDA%3D',
    text: 'Tự hào vì đã là một phần của tập thể tuyệt vời này. Chúc mọi người thật nhiều may mắn trên chặng đường tiếp theo!',
  },
  {
    id: 6,
    name: 'Nguyễn Anh Kiệt',
    username: 'Frontend Developer',
    avatar: 'https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NDd8fHVzZXJ8ZW58MHx8MHx8fDA%3D',
    text: 'Những ngày tháng làm việc cùng nhau sẽ luôn là một phần đẹp đẽ trong ký ức. Cảm ơn vì tất cả!',
  },
  {
    id: 7,
    name: 'Võ Đăng Phi Long',
    username: 'Frontend Developer',
    avatar: 'https://images.unsplash.com/photo-1489980557514-251d61e3eeb6?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NjB8fHVzZXJ8ZW58MHx8MHx8fDA%3D',
    text: 'Mỗi cuộc gặp gỡ đều có ý nghĩa, và những năm tháng ở đây đã để lại dấu ấn khó quên trong lòng chúng ta.',
  },
]
</script>

<template>
  <div class="mt-10 container p-4">
    <div class="w-full overflow-hidden pb-10 relative">
      <div class="mx-auto flex flex-col items-center">
        <h2 class="mt-10">
          Đại gia đình Wellcare
        </h2>
        <p class="mx-auto mb-12 max-w-5xl">
          Những chia sẻ chân thành từ đội ngũ nhân viên về trải nghiệm làm việc tại công ty
        </p>
      </div>

      <RotatingDial class="absolute right-[100px] top-[200px] xl:right-[100px] xl:top-[420px] dial-rotate" />

      <div class="relative z-10 animate-infinite-scroll group-hover:[animation-play-state:paused] gap-6 grid xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-2 grid-cols-1">
        <div
          v-for="(testimonial, index) in testimonials"
          :key="index"
          class="rounded-lg h-full border shadow-md border-gray-50 bg-white/70 p-5"
        >
          <div class="flex items-center mb-4">
            <img
              :alt="testimonial.name"
              :src="testimonial.avatar"
              class="shrink-0 object-cover rounded-full w-11 h-11  mr-3"
            />
            <div>
              <div class="font-inter-tight font-bold text-gray-800">
                {{ testimonial.name }}
              </div>
              <div>
                <span
                  class="text-sm font-medium text-zinc-500 hover:text-zinc-700 transition"
                >
                  {{ testimonial.username }}
                </span>
              </div>
            </div>
          </div>
          <div class="text-zinc-500 text-base">
            {{ testimonial.text }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dial-rotate {
  transform-origin: center;
  backgroud: #ff6f58;
  animation: rotateSmooth 2s ease-in-out infinite;
}

@keyframes rotateSmooth {
  0% {
    transform: rotate(-90deg);
  }
  50% {
    transform: rotate(-180deg);
  }
  100% {
    transform: rotate(-90deg);
  }
}
</style>
