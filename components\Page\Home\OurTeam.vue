<script setup lang="ts">
const { t } = useI18n({ useScope: 'local' })
const src
  = 'https://storage.googleapis.com/cms-gallery/664b064f1a6414290d1e15b5/danh-sach-bac-si-3.png'
const { sprites } = useImageSprite(
  src,
  {
    rows: 4,
    cols: 7,
  },
  { width: 120 * 7, height: 120 * 4 },
)
const { sprites: mobileSprites } = useImageSprite(
  src,
  {
    rows: 4,
    cols: 7,
  },
  { width: 80 * 7, height: 80 * 4 },
)
const achievements = ref([
  {
    name: t('traction:1:description'),
    number: '17.7',
    unit: t('traction:1:unit'),
  },
  {
    name: t('traction:2:description'),
    number: '13.7 ',
    unit: t('traction:2:unit'),
  },
  {
    name: t('traction:3:description'),
    number: '40+',
    unit: '',
  },
  {
    name: t('traction:4:description'),
    number: '100+',
    unit: '',
  },
])
const diff = 20
const mobileDiff = 4

const transRange = [`-mt-${diff}`, `mt-${diff}`, `-mt-${diff}`]
const mobileTransRange = [
  `-mt-${mobileDiff}`,
  `mt-${mobileDiff}`,
  `-mt-${mobileDiff}`,
]
</script>

<template>
  <div
    class="mx-4 my-32 flex flex-col justify-center gap-20 text-center sm:flex-row sm:gap-10 lg:gap-28"
  >
    <div class="self-center">
      <h2 class="mb-4">
        {{ t('traction:title') }}
      </h2>
      <h5 class="mx-auto mb-12 max-w-md">
        {{ t('traction:subtitle') }}
      </h5>
      <div
        class="mx-auto grid grid-cols-2 grid-rows-2 gap-2 text-start md:gap-y-8"
      >
        <Card
          v-for="item in achievements"
          :key="item.name"
          :pt="{
            root: 'group mx-auto bg-gray-50 dark:bg-gray-900 ease-in-out transition-all hover:border hover:border-solid hover:border-[#009688] hover:bg-gradient-to-t from-[#00968860]  via-[#0096881A] to-transparent p-4 rounded-full w-40 h-40 md:h-48 md:w-48 xl:h-60 xl:w-60 grid place-items-center cursor-pointer',
            title:
              'flex items-baseline justify-center group-hover:text-primary',
            subtitle:
              'text-center font-normal text-sm text-surface-500 dark:text-white/60',
          }"
        >
          <template #title>
            <h2 class="group-hover:text-primary text-center">
              {{ item.number }}
            </h2>
            <span class="text-xs md:text-sm">{{ item.unit }}</span>
          </template>
          <template #subtitle>
            {{ item.name }}
          </template>
        </Card>
      </div>
    </div>
    <div class="flex flex-row justify-center gap-4">
      <div
        v-for="(i, index) in transRange"
        :key="i"
        :class="`xs:flex hidden flex-col justify-center gap-4 ${i}`"
      >
        <Avatar
          v-for="(sprite, tindex) in mobileSprites.slice(
            index * 3,
            index * 3 + 3,
          )"
          :key="tindex"
          class="shadow-xl lg:hidden"
          :style="sprite.style"
        />
        <Avatar
          v-for="(sprite, tindex) in sprites.slice(index * 3, index * 3 + 3)"
          :key="tindex"
          class="hidden shadow-xl lg:block"
          :style="sprite.style"
        />
      </div>
      <div
        v-for="(i, index) in mobileTransRange"
        :key="i"
        :class="`xs:hidden flex flex-col gap-4 ${i}`"
      >
        <Avatar
          v-for="(sprite, tindex) in mobileSprites.slice(
            index * 3,
            index * 3 + 3,
          )"
          :key="tindex"
          class="shadow-xl"
          :style="sprite.style"
        />
      </div>
    </div>
  </div>
</template>

<i18n lang="yaml">
en:
  'traction:title': 'Our team of doctors and therapists'
  'traction:subtitle': 'Highly skilled and ethical medical professionals who practice evidence-based medicine.'
  'traction:1:unit': 'years'
  'traction:1:description': 'Average Experience'
  'traction:2:unit': 'minutes'
  'traction:2:description': 'Average Duration of Consultation'
  'traction:3:unit': 'Specialties'
  'traction:3:description': 'Specialties'
  'traction:4:unit': ''
  'traction:4:description': 'Selected Doctors'
vi:
  'traction:title': 'Đội ngũ các bác sĩ và nhà trị liệu'
  'traction:subtitle': 'Giàu kinh nghiệm và tận tâm, cùng thực hành y học chứng cứ.'
  'traction:1:unit': 'năm'
  'traction:1:description': 'Năm kinh nghiệm trung bình'
  'traction:2:unit': 'phút'
  'traction:2:description': 'Thời lượng tư vấn trung bình'
  'traction:3:unit': ''
  'traction:3:description': 'Chuyên khoa'
  'traction:4:unit': ''
  'traction:4:description': 'Bác sĩ'
</i18n>
