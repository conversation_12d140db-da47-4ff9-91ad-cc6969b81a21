type PropertyValue = number | string | boolean
type RelationshipType = 'CONTAINS' | 'DEPENDENT_ON' | 'NEXT'
/**
 * Represents a node in a graph, compatible with Neo4j database.
 */
export interface GraphNode<
  P = Record<string, PropertyValue>,
  L = 'subgraph' | string,
> {
  /**
   * Unique identifier for the node.
   */
  id: string

  /**
   * Labels associated with the node.
   * These are used in Neo4j to categorize nodes.
   */
  labels?: L[]

  /**
   * Indicates if this node represents a subgraph.
   * Default is false.
   */
  isSubgraph?: boolean

  /**
   * Optional weight associated with the node.
   * This weight signifies the importance or significance of the node.
   */
  weight?: number

  /**
   * Properties of the node.
   * These properties are stored as key-value pairs.
   */
  properties?: P
}

/**
 * Represents an edge connecting two nodes in a graph, compatible with Neo4j database.
 */
export interface GraphEdge<
  P = Record<string, PropertyValue>,
  T = RelationshipType,
> {
  /**
   * Identifier of the node where the edge starts.
   */
  from: string // Node Id

  /**
   * Identifier of the node where the edge ends.
   */
  to: string // Node Id

  /**
   * Type of the relationship.
   * This is used in Neo4j to categorize relationships.
   */
  type: T

  /**
   * Optional weight associated with the edge.
   * This weight signifies the cost or distance of traversing the edge.
   */
  weight?: number | ((...args) => number)

  /**
   * Properties of the edge.
   * These properties are stored as key-value pairs.
   */
  properties?: P
}
