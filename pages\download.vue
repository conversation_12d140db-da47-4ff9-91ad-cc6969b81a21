<script setup lang="ts">
const { t, locale } = useI18n({ useScope: 'local' })
useSeoMeta({
  title: t('title'),
  ogTitle: t('title'),
  description: t('meta:description'),
  ogDescription: t('meta:description'),
  ogLocale: locale,
  ogImage: '/images/qr-codes/khamtuxa-download.png',
})

const platform = computed(() => {
  let platform = 'Unknown'

  try {
    const userAgent = navigator.userAgent
    if (/Android/i.test(userAgent)) platform = 'Android'
    else if (/iPhone|iPad|iPod/i.test(userAgent)) platform = 'iOS'
    else if (/Windows/i.test(userAgent)) platform = 'Windows'
    else if (/Mac/i.test(userAgent)) platform = 'Mac'
  }
  catch (e) {
    console.error(e)
  }

  return platform
})
</script>

<template>
  <div class="flex flex-col sm:flex-row p-4 md:p-12 lg:p-24 justify-around">
    <div class="flex flex-col justify-center sm mr-3">
      <h1 class="mt-6 mr-20 sm:mr-40">
        {{ t('title') }}
      </h1>
      <h5>{{ t('meta:description') }}</h5>
      <PlatformDownload
        class="mt-5 flex flex-col sm:flex-row items-center gap-4"
        :width="208"
        :height="128"
        :platform="platform"
        platform-class="hover:shadow-2xl dark:!shadow-white/50 hover:-translate-y-2 duration-500 transition-all rounded-md"
      />
    </div>
    <div class="flex flex-col justify-center text-center">
      <h5 class="mt-5 mb-2 text-2xl font-semibold">
        {{ t('scan-qr:title') }}
      </h5>
      <Image
        class="size-60 md:size-80 mx-auto"
        src="/images/qr-codes/khamtuxa-download.png"
        :pt="{
          image: 'rounded-lg',
        }"
      />
    </div>
  </div>
</template>

<i18n lang="yaml">
en:
  'title': 'Download App'
  'meta:description': 'Expert care at your fingertips. Download now.'
  'scan-qr:title': 'Scan with QR code'
vi:
  'title': 'Tải ứng dụng'
  'meta:description': 'Những lời khuyên sức khỏe đúng đắn chỉ cách bạn một cú chạm tay. Tải ứng dụng ngay!'
  'scan-qr:title': 'Quét mã QR code'
</i18n>
