<script setup lang="ts">
import type { PropType } from 'vue'
import type { Hit, NotionPage } from '~/models'

type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
defineProps({
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['title', 'sapo'],
  },
  article: {
    type: Object as PropType<Hit<NotionPage>>,
    required: true,
  },
  slug: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <article class="xs:flex-row flex flex-col gap-4 px-3 md:gap-5">
    <slot
      v-if="blocks.includes('thumbnail')"
      name="thumbnail"
    >
      <div
        class="h-[150px] flex-[4] overflow-hidden rounded-xl hover:cursor-pointer"
      >
        <NuxtImg
          :src="(article as Hit<NotionPage>).page?.cover?.url"
          :alt="(article as Hit<NotionPage>).page?.properties?.Name"
          format="webp"
          loading="lazy"
          preset="thumbnail"
          width="320px"
          sizes="sm:160px md:200px lg:320px"
          class="aspect-[16/9] object-cover transform transition duration-800 ease-in hover:scale-105 h-full"
        />
      </div>
    </slot>

    <div
      :class="
        blocks.includes('thumbnail')
          ? 'flex-[8]'
          : 'flex-[12]' + ' flex flex-col p-4 align-top'
      "
    >
      <slot
        v-if="blocks.includes('title')"
        name="title"
      >
        <NuxtLink :to="`${slug}/${article.page.properties.Slug}`">
          <div
            class="hover:text-primary mb-2 text-xl font-bold tracking-tight text-gray-900 md:text-2xl dark:text-white"
          >
            {{ article?.page?.properties?.Name }}
          </div>
        </NuxtLink>
      </slot>

      <slot
        v-if="blocks.includes('sapo')"
        name="sapo"
      >
        <p class="mb-3 font-normal text-gray-700 dark:text-gray-400">
          {{ article?.page?.properties?.Sapo }}
        </p>
      </slot>
      <slot name="readtime">
        <p></p>
      </slot>
    </div>
  </article>
</template>
