# Hướng dẫn tạo Responsive Design với Tailwind CSS

Trong bài viết này, chúng ta cần thống nhất với nhau về quy chuẩn thiết lập giới hạn max-width với TailwindCSS, tránh trường hợp căn lề theo sở thích như:

- Người thì dùng max-width theo cảm tính.
- Người thì dùng grid
- Người thì dùng margin/padding horizontal
  -> Khiến cho mỗi page sẽ có mỗi kiểu style khác nhau không đồng bộ về lề theo chiều ngang

## Tham khảo

Đọc thêm bài viết trước khi bắt đầu: https://www.notion.so/wellcare-vn/Design-tips-41c8840b60ea4cee9c4d8fa79e2cf30c

C<PERSON> thể thấy rằng, max-width: 1280px là con số mà phần lớn người dùng yêu thích (có thể là theo kinh nghiệm của họ thấy vậy)

<PERSON><PERSON> chắc chắn hơn, chúng ta cùng tham khảo một số website đang sử dụng Tailwind để tham khảo xem họ xử lý như nào:

### TailwindCSS

<img src="./tailwind-screen.png">
Người ta dùng con số 1440px làm max-width (max-w-8xl mx-auto)

### Nuxt3 và Buildui.com

<img src="./nuxt-screen.png">
<img src="./buildui-screen.png">
Nuxt và Buildui đều sử dụng 'con số vàng' 1280px, sau đó thêm padding tùy chỉnh hai bên

### Aceterbity UI

<img src="./Aceterbity-screen.png">
Trang này config vào container class của Tailwind và dùng max-width: 1440px

```css
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}

@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
  }
}
```

VD1:

```html
<div class="container mx-auto sm:px-4 md:px-6 lg:px-8 xl:px-10">
  <!-- Nội dung -->
</div>
```

VD2:

```html
<div class="container mx-auto">
  <div
    class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4"
  ></div>
</div>
```

Trong ví dụ trên, số cột sẽ thay đổi tùy theo kích thước màn hình, từ 1 cột ở breakpoint sm, 2 cột ở breakpoint md, 3 cột ở breakpoint lg và 4 cột ở breakpoint xl.

## Kết luận:

- 1280px là con số mà phần lớn mọi người yêu thích. Tuy nhiên chúng ta cần nới rộng ra một xíu đối với các máy lớn như máy Anh Phong, Anh Khoa. Cho nên thông số cuối cùng sẽ là max-width: 1440px;
- Chúng ta sẽ mượn ý tưởng của Aceterbity UI và custome lại class '.container' của Tailwind https://tailwindcss.com/docs/container để làm class mặc định đầu vào cho mỗi trang (đoạn nào dùng background cover như Flowbite-container thì không cần bọc vào nếu bị xấu).
