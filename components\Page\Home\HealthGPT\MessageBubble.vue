<script setup lang="ts">
interface Action {
  label?: string
  icon: string
  tooltip: string
}
interface Props {
  message: string
  avatar: string
  actions?: Action[]
  side: 'left' | 'right'
}

const props = defineProps<Props>()
</script>

<template>
  <div
    :class="[
      'flex items-end gap-2',
      side == 'left' ? 'mr-14 flex-row' : 'ml-14 flex-row-reverse',
    ]"
  >
    <NuxtImg
      :src="props.avatar"
      alt="Avatar"
      class="size-8 flex-none rounded-full"
      style="background-color: aliceblue"
    />
    <div
      :class="[
        'relative space-y-2 whitespace-pre-line rounded-t-xl bg-gray-100 p-3 dark:bg-gray-800',
        side == 'left'
          ? 'rounded-bl rounded-br-xl'
          : 'rounded-bl-xl rounded-br',
      ]"
    >
      <p>
        {{ props.message }}
      </p>

      <slot name="footer"></slot>

      <div
        v-if="props.actions"
        class="flex flex-row items-center gap-2"
      >
        <button
          v-for="action in actions"
          :key="action.label"
          v-tooltip.bottom="action.tooltip"
          class="text-gray-600 dark:text-surface-300 py-1 px-1.5 flex gap-1 flex-row items-center hover:bg-gray-200 dark:hover:bg-gray-700 transition rounded text-sm sm:text-base"
        >
          <i :class="[action.icon, 'sm:text-lg']"></i>
          <p>{{ action.label }}</p>
        </button>
      </div>
    </div>
  </div>
</template>
