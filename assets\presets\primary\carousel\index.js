export default {
  root: {
    class: [
      // Flexbox
      'flex flex-col',
    ],
  },
  content: {
    class: [
      // Flexbox & Overflow
      'flex flex-col overflow-auto',
    ],
  },
  container: ({ props }) => ({
    class: [
      // Flexbox
      'flex',

      // Orientation
      {
        'flex-row': props.orientation !== 'vertical',
        'flex-col': props.orientation == 'vertical',
      },
    ],
  }),
  previousbutton: {
    class: [
      // Flexbox & Alignment
      'flex justify-center items-center self-center',

      // Sizing & Overflow
      'overflow-hidden p-2 md:p-4',

      // Spacing
      'lg:mr-4 mr-2 ml-2',

      // Shape
      'rounded-full',

      // Border & Background
      'border-0 bg-slate-100 dark:bg-slate-800',

      // Color
      'hover:bg-slate-200 dark:hover:bg-slate-700',

      // Transitions
      'transition-all',
    ],
  },
  nextbutton: {
    class: [
      // Flexbox & Alignment
      'flex justify-center items-center self-center',

      // Sizing & Overflow
      'overflow-hidden p-2 md:p-4',

      // Spacing
      'lg:ml-4 ml-2 mr-2',

      // Shape
      'rounded-full',

      // Border & Background
      'border-0 bg-slate-100 dark:bg-slate-800',

      // Color
      'hover:bg-slate-200 dark:hover:bg-slate-700',

      // Transitions
      'transition-all',
    ],
  },
  itemscontent: {
    class: [
      // Overflow & Width
      'overflow-hidden w-full',
    ],
  },
  itemscontainer: ({ props }) => ({
    class: [
      // Flexbox
      'flex',

      // Orientation & Sizing
      {
        'flex-row': props.orientation !== 'vertical',
        'flex-col h-full': props.orientation == 'vertical',
      },
    ],
  }),
  item: ({ props }) => ({
    class: [
      // Flexbox
      'flex justify-center',

      // Size
      {
        'w-full sm:w-[50%] md:w-[33.333333333333336%]':
          props.orientation !== 'vertical',

        'w-full h-full': props.orientation == 'vertical',
      },
    ],
  }),
  itemcloned: ({ props }) => ({
    class: [
      // Flexbox
      'flex shrink-0 grow',
      'unvisible',

      // Size
      {
        'w-full sm:w-[50%] md:w-[33.333333333333336%]':
          props.orientation !== 'vertical',

        'w-full h-full': props.orientation == 'vertical',
      },
    ],
  }),
  indicators: {
    class: [
      // Flexbox & Alignment
      'flex flex-row gap-2 justify-center flex-wrap mt-2',
    ],
  },
  indicator: {
    class: [
      // Spacing
      // 'mr-2 mb-2',
    ],
  },
  indicatorbutton: ({ context }) => ({
    class: [
      // Sizing & Shape
      'w-[10px] h-[10px] rounded-full',

      // Transitions
      'transition-all duration-200',

      // Focus Styles
      'focus:outline-none focus:outline-offset-0 focus:ring focus:ring-primary/50 dark:focus:ring-primary/50',

      // Color & Background
      {
        'bg-surface-200 hover:bg-surface-300 dark:bg-surface-700 dark:hover:bg-surface-600':
          !context.highlighted,
        'bg-primary w-[10px] h-[10px] hover:bg-primary': context.highlighted,
      },
    ],
  }),
}
