<script setup lang="ts">
const { t } = useI18n({ useScope: 'local' })
type BlockType = 'brand-description' | 'brand-tagline'
const emit = defineEmits(['action:close'])
const { size } = defineProps({
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['brand-description'],
  },
  size: {
    type: Number,
    default: 10,
  },
  hasTitle: {
    type: Boolean,
    default: true,
  },
  hasCloseBtn: {
    type: Boolean,
    default: false,
  },
})
</script>

<template>
  <div class="flex flex-col">
    <div class="flex flex-row items-center text-sm">
      <NuxtImg
        src="/images/logo.svg"
        alt="Logo"
        :class="`size-${size} my-auto mr-2`"
        loading="lazy"
      />
      <span
        :class="
          (size > 10 ? 'text-3xl' : 'text-2xl') + ' flex-grow font-semibold'
        "
      >Wellcare</span>
      <Button
        v-if="hasCloseBtn"
        size="small"
        icon="pi pi-times"
        severity="secondary"
        text
        rounded
        @click="emit('action:close', false)"
      />
    </div>
    <span
      v-if="blocks.includes('brand-tagline')"
      class="text-primary mb-2 mt-3 block font-bold"
    >
      {{ t('brand:tagline') }}
    </span>
    <span
      v-if="blocks.includes('brand-description')"
      class="text-sm"
    >
      {{ t('brand:description') }}
    </span>
  </div>
</template>

<i18n lang="yaml">
vi:
  'brand:tagline': 'Đối tác sức khỏe TIN CẬY'
  'brand:description': 'Chúng tôi giúp bạn duy trì một lối sống lành mạnh, và khi bạn cần tham vấn y tế, chúng tôi kết nối bạn với những bác sĩ chuyên khoa hàng đầu qua gọi thoại và gọi video.'
en:
  'brand:tagline': 'Your TRUSTED health partner'
  'brand:description': 'We help you maintain a good health and when you have problems we connect you with the best specialists.'
</i18n>
