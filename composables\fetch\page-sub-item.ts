import { useFetchDefault } from './default'
import { useNuxtApp } from '#imports'

import type {
  HitNotionWebsite,
  IFilter,
  ISource,
  ResponseElasticSearch,
  TransformElasticHits,
  ValueOrRef,
} from '~/models'
import { ElasticIndex } from '~/models'

export function useFetchPageSubItem(input: {
  page?: {
    filters: ValueOrRef<IFilter[]>
    from?: ValueOrRef<number>
    size?: ValueOrRef<number>
  }
  subitem?: {
    filters?: ValueOrRef<IFilter[]>
    from?: ValueOrRef<number>
    size?: ValueOrRef<number>
    _source?: ISource
    collapse?: string
  }
}) {
  const { $fetchWellcare } = useNuxtApp()

  const { page, subitem } = input
  const fetchDefault = useFetchDefault(ElasticIndex.NOTION_WEBSITE)
  const { filters, from, size } = page || {}
  const computedPageFilters = isRef(filters) ? filters : computed(() => filters)
  const computedPageFrom = isRef(from) ? from : computed(() => from || 0)
  const computedPageSize = isRef(size) ? size : computed(() => size || 10)
  const {
    // filters: subitemFilters,
    from: subitemFrom,
    size: subitemSize,
  } = subitem || {}
  // const computedSubFilters = isRef(subitemFilters)
  //   ? subitemFilters
  //   : computed(() => subitemFilters)
  const computedSubFrom = isRef(subitemFrom)
    ? subitemFrom
    : computed(() => subitemFrom || 0)
  const computedSubSize = isRef(subitemSize)
    ? subitemSize
    : computed(() => subitemSize || 5)

  const { data, status, execute } = useAsyncData<any>(
    () => {
      const parents = <Promise<ResponseElasticSearch<HitNotionWebsite>>>(
        $fetchWellcare(ElasticIndex.NOTION_WEBSITE, {
          method: 'post',
          body: {
            from: computedPageFrom.value,
            size: computedPageSize.value,
            query: {
              bool: {
                filter: fetchDefault.filter.concat(computedPageFilters.value),
                should: fetchDefault.should,
              },
            },
            sort: fetchDefault.sort,
            _source: {
              includes: ['page', 'blockstring'],
            },
          },
        })
      )
      const output = new Promise((resolve, reject) => {
        parents
          .then((parentsData) => {
            const subitems = parentsData.body.hits.hits
              .map(i => i._id)
              .filter(i => i)
            return $fetchWellcare<ResponseElasticSearch<HitNotionWebsite>>(
              ElasticIndex.NOTION_WEBSITE,
              {
                method: 'post',
                body: {
                  from: computedSubFrom.value,
                  size: subitems.length,
                  _source: fetchDefault._source,
                  query: {
                    bool: {
                      filter: fetchDefault.filter.concat([
                        {
                          bool: {
                            minimum_should_match: 1,
                            should: subitems.map(i => ({
                              match_phrase: {
                                'page.properties.Parent item.id': i,
                              },
                            })),
                          },
                        },
                      ]),
                      should: fetchDefault.should,
                    },
                  },
                  collapse: {
                    field: 'page.properties.Parent item.id.keyword',
                    inner_hits: {
                      name: 'most_recent',
                      size: computedSubSize.value,
                      from: 0,
                      sort: fetchDefault.sort,
                      _source: subitem._source,
                    },
                    max_concurrent_group_searches: 4,
                  },
                  sort: fetchDefault.sort,
                },
              },
            )
              .then((collapseArticlesData) => {
                // Once both promises are resolved, resolve with categories data
                parentsData.body.hits.hits = parentsData.body.hits.hits.map(
                  (hit) => {
                    hit.inner_hits = collapseArticlesData.body.hits.hits.find(
                      i =>
                        i.fields[
                          'page.properties.Parent item.id.keyword'
                        ].includes(hit._id),
                    )?.inner_hits
                    return hit
                  },
                )
                resolve(parentsData)
              })
              .catch((error) => {
                console.error(error)
                reject(error) // Reject if collapseArticles promise fails
              })
          })
          .catch((error) => {
            console.error(error)
            reject(error) // Reject if categories promise fails
          })
      })

      return output
    },
    {
      transform: transform.hits as () => TransformElasticHits<HitNotionWebsite>,
      watch: [computedPageFilters, computedPageFrom, computedPageSize],
    },
  )
  const loading = computed(() => status.value == 'pending')
  return { data, status, execute, loading }
}
