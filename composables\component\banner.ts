import { useFetchElasticWithDefault } from '../fetch/elastic-with-default'
import type {
  BannerPosition,
  ComponentBanner,
  HitNotionBanners,
} from '~/models'
import { ElasticIndex } from '~/models'

export function useBanner(query: { position: BannerPosition, size: number }) {
  const { path } = useRoute()
  const { push } = useRouter()
  const config = useRuntimeConfig()
  const dataLayer = ref({})
  const gtm = useGtm()
  const { data } = useFetchElasticWithDefault<HitNotionBanners>(
    ElasticIndex.NOTION_BANNERS,
    {
      size: Number(query.size),
      filters: [
        {
          term: {
            'page.properties.App.keyword': config.public.page.site,
          },
        },
      ],
    },
  )
  const banners = computed(() => {
    return (
      data.value?.hits
        .filter((hit) => {
          try {
            const regex = new RegExp(hit.page.properties.Condition, 'i')
            return regex.test(path)
          }
          catch {
            console.error(
              `Invalid regex in condition: ${hit.page.properties.Condition}, Error: ${e}`,
            )
            return false
          }
        })
        .map(hit => ({
          weight: hit.page.properties.Weight,
          source: hit.page.properties?.Files?.[0].url,
          target: hit.page.properties.Target,
          position: hit.page.properties.Position,
        })) || []
    )
  })
  const banner: ComputedRef<ComponentBanner> = computed(() => {
    // track banner viewed
    return banners.value.find((banner) => {
      let isShown = false
      if (banner.position === query.position) isShown = true
      if (banner.target === path) isShown = false
      return isShown
    })
  })
  const impress = () => {
    console.log('[track-banner] impress', banner.value)
  }
  const click = () => {
    // track banner clicked
    console.log('[track-banner] clicked', banner.value)

    if (banner.value.target.startsWith('https')) {
      window.open(banner.value.target, '_blank')
    }
    else {
      push({
        path: banner.value.target,
      })
    }

    gtm.push({
      ...dataLayer.value,
      event: 'select_content',
    })
  }

  return { banner, data, click, banners, impress }
}
