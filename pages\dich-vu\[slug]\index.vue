<script setup lang="ts">
import { ElasticIndex } from '~/models'

const route = useRoute()
const slug = computed(() => (route.params as any).slug)
const { hit, loading, status } = useFetchElasticWithDefault(
  ElasticIndex.NOTION_WEBSITE,
  {
    filters: [{ term: { 'page.properties.Slug.keyword': slug.value } }],
    _source: {
      includes: ['page', 'blockstring'],
    },
  },
)
usePageArticle({
  hit: hit,
  status: status,
})

const { t } = useI18n()
const breadcrumbs = computed(() => {
  const output = [{ label: t('breadcrumb:home'), route: '/' }]
  if (hit.value?.page.properties['Parent item'].length > 0) {
    output.push({
      label: hit.value.page.properties['Parent item'][0].properties.Name,
      route: '/' + hit.value.page.properties['Parent item'][0].properties.Slug,
    })
  }
  return output
})

const { hit: questions }: any = useFetchElasticWithDefault(
  ElasticIndex.NOTION_WEBSITE,
  {
    filters: [
      {
        term: {
          'page.properties.Parent item.properties.Slug.keyword': `${slug.value}-faqs`,
        },
      },
    ],
  },
)
</script>

<template>
  <div>
    <div
      v-if="loading || !hit"
      class="mx-auto max-w-7xl p-6"
    >
      <WSkeleton block="article" />
    </div>
    <PageArticle
      v-else
      :breadcrumbs="breadcrumbs"
      :faqs="questions"
      :loading="loading"
      :page="hit?.page"
    />
  </div>
</template>
