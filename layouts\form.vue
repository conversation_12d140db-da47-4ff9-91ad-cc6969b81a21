<script setup lang="ts">
const localeHead = useLocaleHead({
  addDirAttribute: true,
  identifierAttribute: 'id',
  addSeoAttributes: false,
})

useHead({
  htmlAttrs: {
    lang: localeHead.value.htmlAttrs.lang,
    dir: localeHead.value.htmlAttrs.dir,
  },
  link: localeHead.value.link,
  meta: localeHead.value.meta,
})
</script>

<template>
  <div class="relative bottom-0 left-0 right-0 top-0">
    <TopBar :blocks="['ThemeSwitch', 'LocaleSwitch']" />
    <NuxtPage />

    <MainFooter
      brand-name="Wellcare"
      year="2015"
    />
  </div>
</template>
