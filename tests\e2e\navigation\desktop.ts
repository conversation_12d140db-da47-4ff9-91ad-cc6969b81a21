import {
  // afterAll, beforeAll,
  it,
} from 'vitest'
import { createPage } from '@nuxt/test-utils/e2e'
import {
  expect,
  // type ElementHandle,
  // type Page
} from '@playwright/test'

export const DescribeDesktopNavigation = async () => {
  it('All visible links should not result in 404', async () => {
    const page = await createPage('/')

    const links = await page.getByRole('link').all()
    links.forEach(async (link) => {
      page.goto(link.first.toString())
      await expect(page.getByRole('heading', { name: '404' })).toBeHidden()
    })
    await page.close()
  })

  it('should render 404 page when not found', async () => {
    const page = await createPage('/undefined')
    await page.route('/api/search/content', async (route) => {
      const json = { body: { hits: { total: { value: 0 }, hits: [] } } }
      await route.fulfill({ json })
    })
    await expect(page.getByRole('heading', { name: '404' })).toBeVisible()
    await page.close()
  })
}

export default DescribeDesktopNavigation
