import type { NotionPage, NotionWebsitePageProperties } from '~/models'

export function usePageLink(page: NotionPage<NotionWebsitePageProperties>) {
  const primary = page.properties['Parent item']
    ? page.properties['Parent item'][0]
    : null
  const breadcrum = ref([])
  const to = computed(() => {
    const parts = [page.properties.Slug]
    if (primary) {
      parts.unshift(primary.properties.Slug)
    }

    return '/' + parts.join('/')
  })
  return { breadcrum, to }
}
