<script setup lang="ts">
import { ElasticIndex } from '~/models'
import { navigateTo } from '#imports'

definePageMeta({
  layout: 'article',
  colorMode: 'light',
  validate: async route => (route.params as any).category !== 'api',
})

const route = useRoute()
const { t } = useI18n()
const { display } = useDisplay()
const switchLocalePath = useSwitchLocalePath()

const slug = computed(() => (route.params as any).slug.join('/'))

const { hit, loading, status } = useFetchElasticWithDefault(
  ElasticIndex.NOTION_WEBSITE,
  {
    size: 1,
    filters: computed(() => [
      { term: { 'page.properties.Slug.keyword': slug.value } },
    ]),
    _source: {
      includes: ['page', 'blockstring'],
    },
  },
)

const breadcrumbs = computed(() => {
  const output = [{ label: t('breadcrumb:home'), route: '/' }]
  if (hit.value?.page.properties['Parent item'].length > 0) {
    output.push({
      label: hit.value.page.properties['Parent item'][0].properties.Name,
      route: '/' + hit.value.page.properties['Parent item'][0].properties.Slug,
    })
  }
  return output
})

const isMobile = computed<boolean>(() => display.breakpoint?.isMobile)

usePageArticle({
  hit,
  status,
})

// Watch for loading state and handle errors
watch(
  [loading, hit],
  () => {
    if (loading.value || hit.value) return

    if (route.fullPath.includes('/benh/')) {
      navigateTo(switchLocalePath('vi'))
    }

    showError({
      statusCode: 404,
      statusMessage: t('error:page-not-found:message'),
      cause: t('error:page-not-found:cause'),
      fatal: true,
    })
  },
  { immediate: true },
)

// Set SEO meta tags based on fetched data
useSeoMeta({
  title: computed(() => `${hit.value?.page?.properties?.Name} - Wellcare`),
  ogTitle: computed(() => `${hit.value?.page?.properties?.Name} - Wellcare`),
  description: computed(
    () => `${hit.value?.page?.properties['Meta Description']}` || '',
  ),
  ogDescription: computed(
    () => `${hit.value?.page?.properties['Meta Description']}` || '',
  ),
  ogImage: computed(() => `${hit.value?.page?.cover?.url}` || ''),
})
</script>

<template>
  <div
    :class="[
      {
        'pt-2': isMobile,
      },
    ]"
  >
    <div
      v-if="loading || !hit"
      class="mx-auto max-w-7xl p-6"
    >
      <WSkeleton block="article" />
    </div>
    <template v-else>
      <PageArticle
        :breadcrumbs="breadcrumbs"
        :page="hit?.page"
        :loading="loading"
      />
    </template>
  </div>
</template>
