<script setup lang="ts">
const localeHead = useLocaleHead({
  addDirAttribute: true,
  identifierAttribute: 'id',
  addSeoAttributes: true,
})

const { t, locale } = useI18n()

const route = useRoute()
const router = useRouter()

const openSearch = computed<boolean>(() => route.query?.action === 'search')

function onOpenSearch() {
  router.push({
    query: {
      action: 'search',
      target: locale.value === 'vi' ? '/tim-kiem' : '/en/search',
    },
  })
  setTimeout(() => {
    const el: HTMLInputElement | null
      = document.querySelector('#search_input_el')
    if (el) el.focus()
  }, 300)
}

useHead({
  htmlAttrs: {
    lang: localeHead.value.htmlAttrs.lang,
    dir: localeHead.value.htmlAttrs.dir,
  },
  link: localeHead.value.link,
  meta: localeHead.value.meta,
})
</script>

<template>
  <div class="flex min-h-screen flex-col">
    <AppBar
      :blocks="['Logo', 'Search', 'CTA']"
      :cta="{ label: t('btn:download'), route: 'download' }"
      @action:search="onOpenSearch"
    />
    <template v-if="openSearch">
      <div
        class="fixed inset-0 z-20"
        style="backdrop-filter: blur(15px)"
        @click="
          $router.push({
            query: {},
          })
        "
      ></div>
      <WAppSearch class="!fixed w-full" />
    </template>

    <!-- Main content -->
    <main class="flex-1">
      <NuxtPage />
    </main>

    <LazyWBanner position="popup" />
    <MainFooter
      brand-name="Wellcare"
      year="2015"
    />
  </div>
</template>
