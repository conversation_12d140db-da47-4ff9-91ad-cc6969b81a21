import colors from 'tailwindcss/colors'

/** @type {import('tailwindcss').Config} */
export default {
  content: [],
  darkMode: 'class',
  plugins: [],
  theme: {
    extend: {
      screens: {
        xs: '475px',
      },
      colors: {
        rating: 'rgb(var(--rating))',
        primary: {
          50: '#effefb',
          100: '#c7fff4',
          200: '#90ffe9',
          300: '#51f7dc',
          400: '#1de4ca',
          500: '#04c8b1',
          600: '#009688',
          700: '#058075',
          800: '#0a655f',
          900: '#0d544f',
          950: '#003332',
          DEFAULT: '#009688',
        },
        secondary: {
          50: '#fef4ee',
          100: '#fde7d7',
          200: '#fbcaad',
          300: '#f8a579',
          400: '#f47643',
          500: '#f15b2a',
          600: '#e13a15',
          700: '#bb2913',
          800: '#952217',
          900: '#781f16',
          950: '#410c09',
          DEFAULT: '#f15b2a',
        },
        surface: colors.gray,
      },
    },

  },
  // safelist: ['text-[#f5375d]', 'text-[#00a255], text-[#d31c46]', 'text-[#0f86e1]', 'text-[#00a255]'],
  safelist: ['text-[#ff3946]'],
}
