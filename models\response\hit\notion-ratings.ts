import type { HitNotion, NotionPage } from './notion'

export interface NotionRatingsPageProperties {
  Rating?: number
  Comment?: string
  RatingByName?: string
  RatingByGender?: string
  RatingByAvatar?: {
    url?: string
  }
  IsFeature?: boolean
  ShowOnHomepage?: boolean
  Provider?: string
  CreatedAt: {
    start: string
  }
}

export interface HitNotionRatings extends HitNotion {
  page?: NotionPage<NotionRatingsPageProperties>
}
