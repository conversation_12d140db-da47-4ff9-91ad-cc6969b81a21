<script setup lang="ts">
defineProps({
  items: {
    type: Array<any>,
    required: true,
    default: () => [],
  },
  isMobile: {
    type: Boolean,
    default: () => false,
  },
})

const { t } = useI18n()
const router = useRouter()

const onClick = (item: any) => {
  const faqSlug = item?.page?.properties?.Slug
  router.push({ path: `/cau-hoi-thuong-gap/${faqSlug}` })
}
</script>

<template>
  <Accordion
    expand-icon="pi pi-angle-down"
    collapse-icon="pi pi-angle-up"
  >
    <AccordionTab
      v-for="(item, index) in items"
      :key="index"
      :multiple="true"
      :header="item?.page?.properties?.Name"
      :pt="
        isMobile
          ? {
            headerTitle: '!leading-6',
          }
          : {
            root: {
              class:
                '!mb-0 dark:border-slate-800 md:hover:bg-slate-100 md:dark:hover:bg-slate-900 last:border-b-0 cursor-pointer',
            },
            headerAction: {
              class: 'px-0 py-0 !text-lg',
            },
            headerTitle: {
              class: '!leading-6 sm:leading-none',
            },
          }
      "
    >
      <div>
        <p>{{ item?.page?.properties?.Sapo }}</p>
        <div class="mt-4 flex justify-end">
          <Button
            :label="t('btn:detail')"
            @click="onClick(item)"
          />
        </div>
      </div>
    </AccordionTab>
  </Accordion>
</template>

<i18n lang="yaml">
en:
  'btn:detail': 'Details'
vi:
  'btn:detail': 'Chi tiết'
</i18n>
