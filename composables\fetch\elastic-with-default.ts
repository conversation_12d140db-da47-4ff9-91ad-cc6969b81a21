import { useFetchDefault } from './default'
import { useFetchElastic, computed, isRef } from '#imports'
import type {
  <PERSON>ilter,
  IFunctionScore,
  IHighlight,
  IMust,
  IShould,
  ISource,
  TSort,
  ValueOrRef, ElasticIndex,
} from '~/models'

export function useFetchElasticWithDefault<T>(
  index: ElasticIndex,
  query: {
    _source?: ValueOrRef<ISource>
    filters?: ValueOrRef<IFilter[]>
    from?: ValueOrRef<number>
    function_score?: ValueOrRef<IFunctionScore>
    highlight?: ValueOrRef<IHighlight>
    key?: ValueOrRef<string>
    must?: ValueOrRef<IMust[]>
    should?: ValueOrRef<IShould[]>
    size?: ValueOrRef<number>
    sort?: ValueOrRef<TSort[]>
  },
) {
  const _default = useFetchDefault(index)

  const computeValue = <V>(_value: ValueOrRef<V>, _default?: V) =>
    isRef(_value) ? _value : computed(() => _value ?? _default)

  return useFetchElastic<T>(index, {
    _source: computed(() => ({
      ..._default._source,
      ...computeValue(query._source).value,
    })),
    filters: computed(() =>
      _default.filter?.concat(computeValue(query.filters).value ?? []),
    ),
    from: computeValue(query.from),
    function_score: computeValue(query.function_score),
    highlight: computeValue(query.highlight),
    key: computeValue(query.key).value,
    must: computeValue(query.must),
    should: computed(() =>
      _default.should?.concat(computeValue(query.should).value ?? []),
    ),
    size: computeValue(query.size),
    sort: computeValue(query.sort, _default.sort),
  })
}
