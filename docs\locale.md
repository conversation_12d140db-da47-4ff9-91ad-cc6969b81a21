Locale convention:

1. Nên ưu tiên locale với useScope=local, thay vì global, dễ development hơn, tối ưu app hơn nên sẽ tốt hơn cho SEO.
2. Key dùng theo scope, thay vì là đúng từ không dấu.
3. Global, chắc chắn dùng chung: btn, navigation, error messages
4. Phân chia section bằng chữ : và - (ko nên dùng .)
5. Dịch mã lỗi của backend. Backend trả về ‘Item Not Found’, đặt tên key là ’error:backend-message:Item not Found’
