import { describe, it, expect } from 'vitest'
import url from '../../server/urls'

describe('server/urls', async () => {
  it('should have /api/ prefix', () => {
    Object.values(url).forEach(
      (endpoint: string | ((slug: string) => string)) => {
        if (typeof endpoint === 'string') {
          expect(endpoint.startsWith('/api/')).toBe(true)
        }
        else {
          expect(endpoint('any').startsWith('/api/')).toBe(true)
        }
      },
    )
  })
})
