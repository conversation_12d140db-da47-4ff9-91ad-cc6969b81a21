export default {
  root: {
    class: [
      // Sizing and Shape
      'min-w-60',
      'rounded-md',
      // Spacing
      'p-0 ',
      // Colors
      // 'bg-slate-100 dark:bg-surface-700',
      'text-surface-700 dark:text-white/80',
      // 'border border-surface-200 dark:border-surface-700'
    ],
  },
  menu: {
    class: [
      // Spacings and Shape
      'list-none',
      'm-0',
      'p-0',
      'outline-none',
    ],
  },
  content: ({ context }) => ({
    class: [
      // Typography
      'text-md',
      //Shape
      'rounded-lg',
      // Colors
      'text-surface-700 dark:text-white/80',
      {
        '!text-primary bg-primary/10 dark:bg-surface-300/10 dark:text-white':
          context.focused,
      },
      // Transitions
      // 'transition-colors',
      // 'duration-200',

      // States
      'hover:text-surface-700 dark:hover:text-white/80',
      'hover:bg-surface-100 dark:bg-surface-700 dark:hover:bg-surface-400/10',
    ],
  }),
  action: {
    class: [
      'relative',
      // Flexbox

      'flex gap-1',
      'items-center',

      // Spacing
      'py-2',
      'px-3',

      // Color
      // 'text-surface-700 dark:text-white/80',

      // Misc
      'no-underline',
      'overflow-hidden',
      'cursor-pointer',
      'select-none',
    ],
  },
  icon: {
    class: [
      // Spacing
      'mr-2',

      // Color
      'text-surface-600 dark:text-white/70',
    ],
  },
  label: {
    class: ['leading-normal'],
  },
  submenuheader: {
    class: [
      // Typo
      'font-bold',
      'text-sm',
      // Spacing
      'm-0',
      'py-1 px-0',
      // Shape
      'rounded-tl-none',
      'rounded-tr-none',
      // Colors
      // 'bg-white dark:bg-surface-700',
      'text-surface-400 dark:text-white',
      // Text Transform
      'uppercase',
    ],
  },
  transition: {
    enterFromClass: 'opacity-0 scale-y-[0.8]',
    enterActiveClass:
      'transition-[transform,opacity] duration-[120ms] ease-[cubic-bezier(0,0,0.2,1)]',
    leaveActiveClass: 'transition-opacity duration-100 ease-linear',
    leaveToClass: 'opacity-0',
  },
}
