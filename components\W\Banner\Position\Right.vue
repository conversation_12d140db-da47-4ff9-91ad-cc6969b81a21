<script setup lang="ts">
import type { ComponentBanner } from '~/models'

const { banner } = defineProps({
  banner: {
    type: Object as PropType<ComponentBanner>,
    default: () => ({}),
  },
})

defineEmits(['click'])
</script>

<template>
  <NuxtImg
    :src="banner.source"
    class="hidden w-full cursor-pointer rounded-md md:block"
    sizes="sm:468 md:596px xl:800"
    loading="lazy"
    @click="$emit('click')"
  />
</template>
