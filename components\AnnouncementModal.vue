<template>
  <div
    v-if="isOpen"
    class="transition-all duration-200 fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
  >
    <div
      ref="popupRef"
      class="bg-white/90 dark:bg-gray-900 p-4 sm:p-8 rounded-lg shadow-lg max-w-xl w-full mx-4 overflow-y-auto"
    >
      <div
        class="flex gap-1"
      >
        <img
          src="../assets/icons/megaphone.png"
          class="w-[18px] h-[18px] mr-1 mt-1.5 object-cover"
        />
        <h2
          class="
          text-red-500
        text-lg
        font-semibold"
        >
          Thông báo quan trọng từ Wellcare
        </h2>
      </div>
      <div class="max-h-[60vh] overflow-y-auto pr-2 text-justify">
        <p class="my-2">
          Wellcare sẽ ngừng hoạt động khám từ xa kể từ <strong>01/03/2025</strong>.
          Tiền thanh toán dư lưu trong sổ khám chỉ có thể dùng để khám từ xa và không thể rút ra
          (<strong>Thông tư 39/2014/TT-NHNN</strong> và <strong>Thông tư 23/2019/TT-NHNN</strong> của Ngân hàng Nhà nước Việt Nam).
        </p>
        <p class="mt-4 my-2">
          - Hệ thống đặt hẹn vẫn tiếp tục duy trì đến 31/03/2025 và chỉ hoạt động để những bệnh nhân đã chuyển tiền dư (tính đến 28/02/2025) tiếp tục sử dụng dịch vụ đến 31/03/2025.
        </p>
        <p class="mt-4 my-2">
          - Từ 01/03/2025, Wellcare không hỗ trợ chuyển khoản hoặc thanh toán mới.
        </p>
        <p class="my-2">
          - Người dùng đã là thành viên vẫn có thể dùng các quyền lợi Health GPT, EduHub, Health Programs trên ứng dụng cho đến ngày hết hạn.
        </p>
      </div>
      <div class="sticky bottom-0 pt-4 pb-2 bg-transparent">
        <button
          class="w-full bg-gray-300 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-200 transition font-semibold"
          @click="isOpen = false"
        >
          Đóng
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { useAnnouncement } from '~/composables'

const isOpen = useAnnouncement()
const popupRef = ref<HTMLElement | null>(null)

onClickOutside(popupRef, () => (isOpen.value = false))
</script>
