<script setup lang="ts">
import type { PropType } from 'vue'
import { useFetchForms } from '~/composables/fetch/forms'
import type {
  NotionWebsitePageProperties,
  MenuItem,
  NotionPage,
} from '~/models'

const props = defineProps({
  breadcrumbs: {
    type: Array as PropType<MenuItem[]>,
    default: () => [],
  },
  faqs: {
    type: Object,
    default: () => {},
  },
  loading: {
    type: Boolean,
    default: false,
  },
  page: {
    type: Object as PropType<NotionPage<NotionWebsitePageProperties>>,
    required: true,
  },
})
const page: ComputedRef<NotionPage<NotionWebsitePageProperties>> = computed(
  () => props.page,
)
const computedBreadcrumbs = computed(() => {
  return props.breadcrumbs.concat([
    {
      label: page.value?.properties?.Name,
    },
  ])
})
const { query } = useRoute()
const isEditorEnabled = computed(() => query?.editor == 'true')
const { form } = useFetchForms({ slug: 'book-appointment' })
</script>

<template>
  <PageArticleEditor
    v-if="isEditorEnabled && page"
    :page="page"
  />
  <div
    :key="page?.id"
    class="relative break-words pb-12"
  >
    <div class="grid grid-cols-1 md:grid-cols-12 xl:grid-cols-12">
      <div
        class="mt-4 px-5 sm:px-5 md:col-span-7 md:col-start-2 md:mt-6 md:px-5 xl:col-span-7 xl:col-start-2 xl:px-0 2xl:px-0"
      >
        <WBanner
          position="top"
          class="mt-5"
        />
        <WBreadcrumb
          :model="computedBreadcrumbs"
          :loading="loading"
          class="border-none !bg-transparent !px-0"
        />
        <WArticleHeader
          class="mt-4 lg:mt-0"
          :page="page"
          :loading="loading"
        />

        <WArticle
          class="mt-4"
          :blocks="page?.blocks"
          :page="page"
          :loading="loading"
          body
        />

        <WBanner
          position="bottom"
          class="my-4 sm:my-8 sm:mb-10"
        />

        <PageArticleFAQ
          :faqs="faqs?.hits"
          :loading="loading"
        />
      </div>
      <div
        class="p-3 md:col-span-3 md:col-start-9 xl:col-span-3 xl:col-start-9 xl:ml-8"
      >
        <WForm
          v-if="form"
          class="bg-surface-50 mt-8 rounded-xl shadow-sm dark:bg-teal-950"
          :form="form"
        />
        <WBanner
          position="right"
          class="mt-5"
        />
      </div>
    </div>
  </div>
</template>
