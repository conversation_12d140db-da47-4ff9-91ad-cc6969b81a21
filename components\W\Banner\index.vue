<script setup lang="ts">
// declare components
import Popup from './Position/Popup.vue'
import Bottom from './Position/Bottom.vue'
import Top from './Position/Top.vue'
import Left from './Position/Left.vue'
import Right from './Position/Right.vue'
import type { BannerPosition } from '~/models'

const { position } = defineProps({
  position: {
    type: String as PropType<BannerPosition>,
    required: true,
  },
  // size: {
  //   type: Number,
  //   default: 1,
  // },
})
const list = {
  popup: Popup,
  bottom: Bottom,
  top: Top,
  left: Left,
  right: Right,
}
const { banner, click, banners, impress } = useBanner({ position, size: 50 })
</script>

<template>
  <component
    :is="list[position]"
    :banner="banner"
    :banners="banners"
    v-bind="$attrs"
    @click="click"
    @impress="impress"
  />
</template>
