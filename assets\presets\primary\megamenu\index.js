export default {
  root: ({ props }) => ({
    class: [
      'relative',

      // Flexbox
      'flex gap-2',

      // <PERSON><PERSON><PERSON> & <PERSON>
      'rounded-none',

      // Color
      'bg-white dark:bg-gray-800',
      'border border-x-0 border-y border-slate-200 dark:border-slate-600',
      {
        'p-2 items-center': props.orientation == 'horizontal',
        'flex-col sm:w-48 p-0 py-1': props.orientation !== 'horizontal',
      },
    ],
  }),
  menu: ({ props }) => ({
    class: [
      // Flexbox
      'sm:flex',
      'items-center',
      'flex-nowrap',
      'gap-x-2',
      'flex-col sm:flex-row',
      { hidden: !props?.mobileActive, flex: props?.mobileActive },

      // Position
      'absolute sm:relative z-0',
      'top-full left-0',
      'sm:top-auto sm:left-auto',

      // Size
      'w-full sm:w-auto',

      // Spacing
      'm-0',
      'pb-1 sm:py-0 sm:p-0',
      'list-none',

      // Shape
      'shadow-md sm:shadow-none',
      'border-0',

      // Color
      'bg-white dark:bg-surface-700 sm:bg-transparent dark:sm:bg-transparent',

      // Misc
      'outline-none',
    ],
  }),
  menuitem: ({ props }) => ({
    class: [
      // 'sm:mr-2',
      'sm:relative static',
      {
        'sm:w-auto w-full': props.horizontal,
        'w-full': !props.horizontal,
      },
      'cursor-pointer',
    ],
  }),
  content: ({ props, context }) => ({
    class: [
      'font-medium',
      // Shape
      { 'sm:rounded-lg': true }, // props.level < 1 && props.horizontal },

      //  Colors
      {
        'text-surface-600 dark:text-white/70':
          !context.focused && !context.active,
        'text-surface-600 dark:text-white/70 bg-slate-100 dark:bg-surface-600/90':
          context.focused && !context.active,
        'text-primary dark:text-white/80 bg-slate-100 dark:bg-primary/30':
          context.focused && context.active,
        'text-primary dark:text-white/80 bg-slate-100 dark:bg-primary/30':
          !context.focused && context.active,
      },

      // Hover States
      {
        'hover:bg-surface-200 dark:hover:bg-surface-600/80': !context.active,
        'hover:bg-slate-200 dark:hover:bg-slate-600 text-primary dark:text-white/80':
          context.active,
      },

      // Transitions
      'transition-all',
      'duration-0',
    ],
  }),
  action: {
    class: [
      'relative',

      // Flexbox
      'flex',
      'items-center',

      // Spacing
      'py-3',
      'px-4',

      // Size
      // 'py-3 pr-5 pl-9 sm:pl-5',
      'leading-normal',

      // Misc
      'select-none',
      'cursor-pointer',
      'no-underline ',
      'overflow-hidden',
    ],
  },
  icon: {
    class: 'mr-2',
  },
  submenuicon: ({ props }) => ({
    class: [
      {
        'ml-auto sm:ml-2': props.horizontal,
        'ml-auto': !props.horizontal,
      },
    ],
  }),
  panel: ({ props }) => ({
    class: [
      // Size
      'w-auto',

      // Spacing
      'py-0',
      'mt-0 md:mt-3',

      // Shape
      'sm:shadow-2xl',
      'sm:rounded-xl',

      // Color
      'bg-white dark:bg-surface-700',

      // Position
      'static sm:absolute',
      'z-20',
      {
        'sm:left-full top-0': !props.horizontal,
      },
    ],
  }),
  grid: {
    class: 'flex flex-wrap sm:flex-nowrap',
  },
  column: {
    class: 'w-full sm:w-1/2',
  },
  submenu: {
    class: ['m-0 list-none', 'pl-2 sm:py-2 sm:px-2 w-full sm:min-w-[18rem]'],
  },
  submenuheader: {
    class: [
      'hidden sm:block',
      'font-semibold',
      'text-lg',

      // Spacing
      // 'py-2 px-2',
      'm-2 mt-0',

      // Color
      'text-surface-700 dark:text-white/80',
      'bg-white dark:bg-surface-700',
    ],
  },
  separator: {
    class: 'border-t border-surface-200 dark:border-surface-600 my-1',
  },
  menubutton: {
    class: [
      // Flexbox
      'flex sm:hidden',
      'items-center justify-center',

      // Size
      'w-10',
      'h-10',

      // Shape
      'rounded-full',
      // Color
      'text-surface-600 dark:text-white/80',

      // States
      'hover:text-surface-600 dark:hover:text-white/60',
      'hover:bg-surface-100 dark:hover:bg-surface-600/80',
      'focus:outline-none focus:outline-offset-0',
      'focus:ring focus:ring-primary/50 dark:focus:ring-primary/50',

      // Transitions
      'transition duration-200 ease-in-out',

      // Misc
      'cursor-pointer',
      'no-underline',
    ],
  },
  end: {
    class: 'flex grow justify-end items-center gap-1',
  },
}
