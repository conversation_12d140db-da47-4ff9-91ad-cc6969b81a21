<script setup lang="ts">
import { ref } from 'vue'

const isDisabled = ref(false)
</script>

<template>
  <div
    v-if="!isDisabled"
    class="relative top-0 z-30 hidden flex-row gap-2 bg-gray-100 p-2 sm:flex dark:bg-gray-900"
  >
    <slot name="start">
      <p class="my-auto ml-4 mr-auto text-sm">
        Wellcare - Your TRUSTED health partner
      </p>
    </slot>
    <slot name="end">
      <div class="flex items-center gap-2">
        <TopBarThemeToggle
          class="my-auto"
          size="small"
        />
        <TopBarLangToggle
          class="my-auto"
          size="small"
        />
        <Button
          size="small"
          icon="pi pi-times"
          plain
          text
          aria-label="close top bar"
          @click="isDisabled = true"
        />
      </div>
    </slot>
  </div>
</template>
