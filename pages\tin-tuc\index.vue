<!-- pages/tin-tuc/index.vue -->
<script setup>
import { ElasticIndex } from '~/models'

const { hit, loading, status } = useFetchElasticWithDefault(
  ElasticIndex.NOTION_WEBSITE,
  {
    size: 1,
    filters: [{ term: { 'page.properties.Slug.keyword': 'tin-tuc' } }],
  },
)
usePageArticle({
  hit: hit,
  status: status,
})
</script>

<template>
  <div>
    <Skeleton
      v-if="loading"
      height="500"
    />
    <PageCategory
      v-else
      layout="timeline"
      slug="tin-tuc"
      :page="hit?.page"
      :blocks="['title', 'sapo', 'published-at']"
      container-list="timeline"
      :fetch="{
        sort: [
          {
            'page.properties.PublishedAt.start': {
              format: 'date_optional_time',
              missing: '_last',
              order: 'desc',
            },
          },
        ],
      }"
    />
  </div>
</template>
