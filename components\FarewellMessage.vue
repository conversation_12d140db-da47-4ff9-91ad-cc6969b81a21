<script lang="ts" setup>
//
const src
  = 'https://storage.googleapis.com/cms-gallery-sandbox/67ac5e31a3987a320f8b7a93/danh-sach-bac-si.png'
const mobileSrc
  = 'https://storage.googleapis.com/cms-gallery-sandbox/67ad6743a3987a1b948b8212/danh-sach-bac-si-mobile.png'
const { sprites } = useImageSprite(
  src,
  {
    rows: 10,
    cols: 10,
  },
  { width: 50 * 10, height: 50 * 10 },
)
const { sprites: mobileSprites } = useImageSprite(
  mobileSrc,
  {
    rows: 17,
    cols: 6,
  },
  { width: 50 * 6, height: 50 * 17 },
)
const diff = 0
const mobileDiff = 0
const transRange = Array(10).fill(`-mt-${diff}`)
const mobileTransRange = Array(6).fill(`-mt-${mobileDiff}`)
</script>

<template>
  <div class="w-full h-auto mx-auto bg-[#fefaf3] px-4 pb-8 xs:px-8 lg:px-16 xl:px-32">
    <div class="pt-10 max-w-7xl w-full mx-auto gap-12 flex flex-col items-center lg:flex-row lg:items-start relative ">
      <div class="text-justify sm:px-12 w-full md:px-0 md:text-left lg:w-1/2">
        <h2>Tri Ân & Tạm Biệt</h2>
        <p class="mt-2 md:-mt-4 text-gray-500">
          12/2014 - 02/2025
        </p>

        <div class="mt-6 flex flex-col space-y-4">
          <p>Với lòng biết ơn sâu sắc, Wellcare xin gửi lời cảm ơn chân thành đến tất cả các bác sĩ đã đồng hành cùng Wellcare trong hơn 10 năm qua. Nhờ sự tận tâm và chuyên môn của các bác sĩ, chúng ta đã có thể mang đến những nội dung tư vấn y khoa giá trị, giúp bệnh nhân tiếp cận với thông tin y tế đúng đắn và tránh được những xét nghiệm, đơn thuốc, can thiệp không cần thiết.</p>

          <p>Tuy nhiên, sau một chặng đường dài kiên trì, Wellcare buộc phải dừng lại vì không thể tiếp tục vượt qua những thách thức tài chính. 10 năm nỗ lực đó đã được bù đắp bằng 2.854 lời cảm ơn mà bệnh nhân để lại trên hệ thống, cùng rất nhiều những lời cảm kích qua điện thoại và tin nhắn riêng, cũng như sự ủng hộ quý báu từ các bác sĩ. Đó là quãng thời gian đáng nhớ, nơi cùng nhau, chúng ta đã tạo nên một Wellcare thực sự đặt sức khỏe bệnh nhân lên hàng đầu.</p>

          <p>Chúng tôi vô cùng trân trọng từng lời góp ý và động viên, từng phút tư vấn, từng chia sẻ chuyên môn, từng sự hỗ trợ dành cho bệnh nhân và Wellcare.</p>

          <p>Mến chào và cầu chúc các bác sĩ sức khỏe, thành công, tiếp tục lan tỏa những giá trị y học chân chính đến cộng đồng.</p>
        </div>
      </div>
      <div class="w-full md:w-2/3 lg:w-1/2 flex flex-row justify-center gap-1.5">
        <div
          v-for="(i, index) in transRange"
          :key="i"
          :class="`sm:flex hidden flex-col justify-center gap-2 ${i}`"
        >
          <Avatar
            v-for="(sprite, tindex) in sprites.slice(index * 10, index * 10 + 10)"
            :key="tindex"
            class="hidden shadow-xl sm:block"
            :style="sprite.style"
          />
        </div>
        <div
          v-for="(i, index) in mobileTransRange"
          :key="i"
          :class="`xs:hidden flex flex-col gap-4 ${i}`"
        >
          <Avatar
            v-for="(sprite, tindex) in mobileSprites.filter((_, i) => i % 6 === index)"
            :key="tindex"
            :alt="`${index} ${tindex}`"
            :class="(index === 4 || index === 5) && tindex === 16 ? 'hidden' : ''"
            class="shadow-xl"
            :style="sprite.style"
          />
        </div>
      </div>
    </div>
  </div>
</template>
