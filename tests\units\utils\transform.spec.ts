// transform.spec.ts
import { describe, it, expect } from 'vitest'
import transform from '../../../utils/transform'

describe('utils/transform', () => {
  const empty = {
    body: {
      hits: {
        hits: [],
        total: { value: 0 },
      },
    },
  }

  it('hit function should return the first hit with parsed blocks', () => {
    const normal = {
      body: {
        hits: {
          hits: [
            {
              id: 1,
              page: { cover: { url: '/link1' } },
              blockstring: JSON.stringify(JSON.stringify(['block1', 'block2'])),
            },
            {
              id: 2,
              page: { cover: { url: '/link2' } },
              blockstring: JSON.stringify(JSON.stringify(['block3', 'block4'])),
            },
          ],
          total: { value: 2 },
        },
      },
    }
    const result = transform.hit(normal)
    expect(result).toEqual({
      id: 1,
      page: {
        cover: { url: '/link1' },
        blocks: ['block1', 'block2'],
      },
    })
  })

  it('hits function should return hits with parsed blocks and total', () => {
    const normal = {
      body: {
        hits: {
          hits: [
            {
              id: 1,
              page: { cover: { url: '/link1' } },
              blockstring: JSON.stringify(JSON.stringify(['block1', 'block2'])),
            },
            {
              id: 2,
              page: { cover: { url: '/link2' } },
              blockstring: JSON.stringify(JSON.stringify(['block3', 'block4'])),
            },
          ],
          total: { value: 2 },
        },
      },
    }
    const result = transform.hits(normal)

    expect(result).toEqual({
      hits: [
        {
          id: 1,
          page: {
            cover: { url: '/link1' },
            blocks: ['block1', 'block2'],
          },
        },
        {
          id: 2,
          page: {
            cover: { url: '/link2' },
            blocks: ['block3', 'block4'],
          },
        },
      ],
      total: 2,
    })
  })

  it('hits function should handle empty hits array', () => {
    const result = transform.hits(empty)

    expect(result).toEqual({
      hits: [],
      total: 0,
    })
  })

  it('hits function should handle empty value', () => {
    const result = transform.hits({})

    expect(result).toEqual({
      hits: [],
      total: 0,
    })
  })
})
