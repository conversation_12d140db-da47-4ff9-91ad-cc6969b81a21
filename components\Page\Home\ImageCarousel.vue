<script setup lang="ts">
const { t } = useI18n()
const autoplay = ref(10000)
const isMounted = ref(false)
const slides = [
  {
    image: {
      src: 'https://storage.googleapis.com/cms-gallery-sandbox/6775fe6cf67f07da3b6590ab/ba-bau-nhe.jpg',
      alt: '',
    },
    title: t('image:1'),
  },
  {
    image: {
      src: 'https://storage.googleapis.com/cms-gallery/6775feab0324b22d3f5cfcdd/me-va-be-nhe.jpg',
      alt: '',
    },
    title: t('image:2'),
  },
  {
    image: {
      src: 'https://storage.googleapis.com/cms-gallery-sandbox/6775fe6af67f07d35a6590a5/benh-man-tinh-nhe.jpg',
      alt: '',
    },
    title: t('image:3'),
  },
  {
    image: {
      src: 'https://storage.googleapis.com/cms-gallery/6775fea80324b234ce5cfcd4/tam-ly-nhe.jpg',
      alt: '',
    },
    title: t('image:4'),
  },
  {
    image: {
      src: 'https://storage.googleapis.com/cms-gallery-sandbox/677601b3f67f07166e6590f9/hr-nhe.jpg',
      alt: '',
    },
    title: t('image:5'),
  },
]
const resources = ref<any[]>(slides.slice(0, 1))
const firstImg = slides[0].image.src

onMounted(() => {
  isMounted.value = true

  setTimeout(() => {
    autoplay.value = 4000
  }, 500 * slides.length)

  slides.slice(1).forEach((slide, index) => {
    setTimeout(() => {
      resources.value = resources.value.concat(slide)
    }, index * 500)
  })
})
</script>

<template>
  <div
    v-if="!isMounted"
    class="relative flex h-full w-full justify-center overflow-hidden"
  >
    <NuxtImg
      :src="firstImg"
      format="webp"
      loading="lazy"
      width="640px"
      sizes="sm:320px md:480px lg:1024px xxl:1536"
      class="h-[68vh] sm:h-[90vh] max-w-none w-full object-cover"
    />
  </div>
  <Carousel
    v-else
    :value="resources"
    circular
    :autoplay-interval="autoplay"
    :pt="{
      content: {
        class: 'relative',
      },
      previousButton: {
        class:
          'absolute z-[1] left-0 top-1/2 -translate-y-1/2 ml-4 px-[13px] py-2 rounded-full hover:bg-black opacity-50',
      },
      previousButtonIcon: {
        class: 'text-white',
      },
      nextButton: {
        class:
          'absolute z-[1] right-0 top-1/2 -translate-y-1/2 mr-4 px-[13px] py-2 rounded-full hover:bg-black opacity-50',
      },
      nextButtonIcon: {
        class: 'text-white',
      },
      indicators: {
        class:
          'absolute z-[1] h-[110px] w-[90%] left-1/2 -translate-x-2/4 pt-1 flex flex-row justify-center left-0 bottom-0 border-white/25 gap-2 border-t pt-8',
      },
      indicatorbutton: ({ context }) => ({
        class: [
          // Sizing & Shape
          'w-[10px] h-[10px] rounded-full',

          // Transitions
          'transition-all duration-200',

          // Focus Styles
          'focus:outline-none focus:outline-offset-0 focus:ring focus:ring-white/50 dark:focus:ring-white/50',

          // Color & Background
          {
            'bg-white/30 hover:bg-gray-100 dark:bg-surface-700 dark:hover:bg-surface-600':
              !context.highlighted,
            'bg-white hover:brightness-150': context.highlighted,
          },
        ],
      }),
    }"
  >
    <template #item="slotProps">
      <div class="relative flex h-full w-full justify-center overflow-hidden">
        <NuxtImg
          :src="slotProps.data.image.src"
          format="webp"
          class="h-[68vh] w-full max-w-none object-cover sm:h-[90vh]"
          sizes="768 lg:1024 xl:2600"
        />
        <div
          class="bg-gradient-top-black-2/10 absolute left-0 top-0 h-full w-full"
        ></div>
        <div
          :class="`absolute left-14 top-1/2 flex max-w-xs -translate-y-1/2 flex-col justify-center align-middle text-white sm:max-w-none lg:left-20`"
        >
          <h1 class="max-w-4xl text-white">
            {{ slotProps.data.title }}
          </h1>
          <h4 class="text-left text-white">
            {{ t("let") }}
            <span
              class="text-primary rounded-lg bg-white/80 px-2 py-[2px] font-bold"
            >Wellcare</span>
            {{ t("help-you") }}
          </h4>
        </div>
      </div>
    </template>
  </Carousel>
</template>

<i18n lang="yaml">
en:
  "image:1": "Are you expectant?"
  "image:2": "Or having little ones of your own?"
  "image:3": "You're having some conditions for a while?"
  "image:4": "You've been going through, and need a therapy?"
  "image:5": "Do you prioritize providing health benefits for employees?"
  "help-you": "help you"
  "let": "Let"
vi:
  "image:1": "Bạn đang mang thai?"
  "image:2": "Hay đã là cha mẹ?"
  "image:3": "Bạn đang cần chăm sóc bệnh mãn tính?"
  "image:4": "Hay muốn được chia sẻ hoặc trị liệu tâm lý?"
  "image:5": "Bạn muốn nhân viên được chăm sóc sức khỏe tốt?"
  "help-you": "giúp bạn"
  "let": "Hãy để"
</i18n>
