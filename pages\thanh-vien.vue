<script setup lang="ts">
defineI18nRoute({
  paths: {
    en: '/membership',
  },
})
const { t, locale } = useI18n({ useScope: 'local' })
useSeoMeta({
  title: t('title'),
  ogTitle: t('title'),
  description: t('meta:description'),
  ogDescription: t('meta:description'),
  ogLocale: locale,
})
</script>

<template>
  <div>
    <p>Benefits Sections</p>
    <p>Choose the plan that fits you</p>
    <p>For corporate</p>
  </div>
</template>

<i18n lang="yaml">
en:
  'title': 'Membership'
  'meta:description': 'Self-empowerment for a healthier and happier you'
  'cover:title': 'Self-empowerment for a healthier and happier you'
  'cover:subtitle': 'Thoughtfully created to support patients and members throughout their care journeys.'
  'features:title': 'Our patient education is researched, transformed, and reviewed by experts. It is accurate, unbiased, and up to date.'
  'features:1:name': 'Accuracy ensured'
  'features:1:detail': 'Our clinical review board consists of ... health professionals and medical specialists from over ... specialty areas.'
  'features:2:name': 'Guaranteed unbiased'
  'features:2:detail': 'We focus on delivering whats best for patients'
  'features:3:name': 'Consistency and quality'
  'features:3:detail': 'Our content is picked by professionals from most reliable and up-to-date resources'
vi:
  'title': 'Chương trình thành viên'
</i18n>
