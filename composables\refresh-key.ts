interface IRefreshKeyOptions {
  watchedSrc?: Array<Ref<any>> | Ref<any> | ComputedRef<any> | null
  immediate?: boolean
}

export const useRefreshKey = ({
  watchedSrc = null,
  immediate = false,
}: IRefreshKeyOptions = {}): {
    key: Ref<number>
    refresh: () => void
  } => {
  const key = ref<number>(0)

  const refresh = () => {
    key.value = Date.now()
  }

  if (watchedSrc) {
    watch(
      watchedSrc,
      () => {
        refresh()
      },
      {
        immediate: immediate,
      },
    )
  }

  return {
    key,
    refresh,
  }
}
