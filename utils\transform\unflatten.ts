// utils/transform/unflatten.ts

export const unflatten = <T>(flattenedObj: { [key: string]: any } = {}) => {
  const unflattenedObj = {} as T
  Object.entries(flattenedObj).forEach(([key, value]) => {
    const keys = key.split('.')
    let tempObj = unflattenedObj
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i]
      tempObj[k] = tempObj[k] || {}
      tempObj = tempObj[k]
    }
    tempObj[keys[keys.length - 1]] = value
  })
  return unflattenedObj
}
