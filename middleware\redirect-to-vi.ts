import { defineNuxtRouteMiddleware, navigateTo } from '#imports'

export default defineNuxtRouteMiddleware(async (to) => {
  if (to.path.startsWith('/en/')) {
    const viPath = to.path.replace('/en/', '/')
    try {
      const response = await fetch(viPath)
      if (response.ok) {
        return navigateTo(viPath, { redirectCode: 302 })
      }
      else {
        return navigateTo('/', { redirectCode: 302 })
      }
    }
    catch (error) {
      console.error('Error fetching page:', error)
      return navigateTo('/', { redirectCode: 302 })
    }
  }
})
