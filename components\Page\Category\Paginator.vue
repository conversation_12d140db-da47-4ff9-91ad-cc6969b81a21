<script setup lang="ts">
const props = defineProps({
  totalRecords: {
    type: Number,
    required: true,
  },
  rows: {
    type: Number,
    default: 20,
  },
})
const { query, path } = useRoute()
const router = useRouter()
const page = parseInt(query.page as string) || 1
const currentPage = ref(page)

const link = []
if (props.totalRecords > currentPage.value * props.rows) {
  link.push({
    rel: 'next',
    href: `${path}?page=${currentPage.value + 1}`,
  })
}
if (currentPage.value >= 2) {
  link.push({
    rel: 'prev',
    href: `${path}?page=${currentPage.value - 1}`,
  })
}
if (currentPage.value == 1) {
  link.push({
    rel: 'prev',
    href: `${path}`,
  })
}
if (currentPage.value > 1) {
  link.push({ rel: 'canonical', href: `${path}?page=${currentPage.value}` })
}
useHead({ link })
const emit = defineEmits(['page'])
const onPageChange = (event) => {
  currentPage.value = event.page + 1
  emit('page', event)
}
watch(currentPage, (newValue) => {
  if (newValue > 1) router.push({ query: { ...query, page: newValue } })
  else router.push({ query: { ...query, page: undefined } })
})
</script>

<template>
  <Paginator
    v-if="totalRecords > rows"
    :rows="rows"
    :total-records="totalRecords"
    :first="(currentPage - 1) * rows"
    :page-link-size="3"
    template="FirstPageLink PrevPageLink PageLinks NextPageLink"
    v-bind="$attrs"
    @page="onPageChange"
  />
</template>
