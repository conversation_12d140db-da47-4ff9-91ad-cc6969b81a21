<script setup lang="ts">
import type { PropType } from '#imports'

interface Specialty {
  key: string
  counter: number
}
const model = defineModel<Record<string, any>>({
  required: true,
})

const props = defineProps({
  specialties: {
    type: Array as PropType<Specialty[]>,
    required: true,
  },
})

const genderList = ref([
  '35 years or older',
  'LGBTQ Friendly',
  'Is male provider',
  'Is Female provider',
])
const selectedOrientation = ref([])

const selectedSpecialty = ref([])
const slots = ref([
  { name: 'All', key: 'A' },
  { name: 'Today', key: 'T' },
  { name: 'This Week', key: 'P' },
  { name: 'This Month', key: 'M' },
])
</script>

<template>
  <div class="flex flex-col items-stretch">
    <Panel
      header="Search by Name"
      toggleable
      :pt="{ root: 'border-none' }"
    >
      <SearchInputText v-model="model.keyword" />
    </Panel>

    <!-- specialties -->
    <Panel
      toggleable
      header="Slots"
      :pt="{ root: 'border-none' }"
    >
      <template #icons>
        <Button
          link
          @click="selectedSpecialty = []"
        >
          Reset
        </Button>
      </template>
      <div
        v-for="category of props.specialties"
        :key="category"
        class="mb-2 flex items-center"
      >
        <Checkbox
          v-model="selectedSpecialty"
          :input-id="category.key"
          name="category"
          :value="category.key"
          class="mr-1"
        />
        <label :for="category.key">{{ category.key }}</label>
      </div>
    </Panel>
    <!-- Gender Orientation -->
    <Panel
      toggleable
      header="Gender Orientation"
      :pt="{ root: 'border-none' }"
    >
      <template #icons>
        <Button
          link
          @click="selectedOrientation = []"
        >
          Reset
        </Button>
      </template>
      <div
        v-for="category of genderList"
        :key="category"
        class="mb-2 flex items-center"
      >
        <Checkbox
          v-model="selectedOrientation"
          :input-id="category"
          name="category"
          :value="category"
          class="mr-1"
        />
        <label :for="category">{{ category }}</label>
      </div>
    </Panel>

    <!-- slot -->
    <Panel
      toggleable
      header="Slots"
      :pt="{ root: 'border-none' }"
    >
      <div
        v-for="slot in slots"
        :key="slot.name"
        class="align-items-center mb-2 flex"
      >
        <RadioButton
          v-model="model.slot"
          :input-id="slot.name"
          name="dynamic"
          :value="slot.name"
        />
        <label
          :for="slot.name"
          class="ml-2"
        >{{ slot.name }}</label>
      </div>
    </Panel>
  </div>
</template>
