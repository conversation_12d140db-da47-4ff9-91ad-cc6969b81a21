<script setup lang="ts">
const { t } = useI18n()
const dayjs = useDayjs()
const { fullPath }: any = useRoute()

const { form } = useFetchForms({ slug: 'knowledge-question' })

const formRef = ref()
const from = ref<number>(0)
const size = ref<number>(10)
const keyword = ref<string>('')

const { questions, total } = usePageHoiBacSi(from, size, keyword)

const onNavigate = (response: any) => {
  navigateTo(
    response.value.navigate
    + `?phoneNumber=${response.value.phoneNumber}&countryCode=84&redirect=${response.value.redirect}`,
    {
      external: true,
      open: {
        target: '_blank',
      },
    },
  )
}

const onFormChange = (event: any) => {
  if (event.key == 'status' && event.value == 'submitted') {
    onNavigate(formRef)
  }
}

const onUpdatedRows = (payload: any) => {
  size.value = payload?.rows
  from.value = payload?.page * size.value
}

const onSeeAnswer = async (slug: string) => {
  await navigateToSlug(`${fullPath}/${slug}`)
}
</script>

<template>
  <div class="mb-4">
    <WHeroSection
      class="h-screen"
      :blocks="['overlay', 'background-image', 'title', 'sub-title', 'under']"
      :background-image="{
        src: 'https://storage.googleapis.com/cms-gallery/66384b1e97e48f5ee5acdc6b/danh-sach-bac-si-5.png',
        alt: 'Ask doctor',
      }"
      :title="t('title')"
      :sub-title="t('subtitle')"
      seo-title-tag="h1"
      small-header
    >
      <template #under>
        <WForm
          v-model="formRef"
          :blocks="['footer']"
          class="bg-surface-100 dark:bg-surface-900 rounded-xl shadow-sm"
          :form="form"
          @change="onFormChange"
        />
      </template>
    </WHeroSection>

    <!-- List ask-doctor question -->

    <p class="mt-10 text-center text-xl font-semibold uppercase md:text-2xl">
      {{ t('questions:title') }}
    </p>
    <div class="mx-auto w-full max-w-5xl md:mb-8">
      <SearchInputText
        v-model="keyword"
        class="container mt-4"
      />
      <NuxtLinkLocale
        v-for="question in questions"
        :key="question._id"
        :to="`${fullPath}/${question.slug}`"
      >
        <!-- hover:bg-surface-200 hover:dark:bg-surface-800 -->
        <Card
          class="border-surface-300 dark:border-surface-600 bg-surface-100 dark:bg-surface-900 m-6 !rounded-md border px-4 lg:container"
        >
          <template #content>
            {{ question.name }}
          </template>
          <template #footer>
            <div class="flex items-center justify-end gap-4">
              <div
                class="flex items-center gap-1 text-sm italic text-gray-500 dark:text-gray-300"
              >
                <i class="pi pi-calendar"></i>
                <span>
                  {{ dayjs(question.last_edited_time).format('LL') }}
                </span>
              </div>

              <Button
                icon="pi pi-eye"
                :label="t('btn:see')"
                @click="onSeeAnswer(question.slug)"
              />
            </div>
          </template>
        </Card>
      </NuxtLinkLocale>
    </div>

    <Paginator
      pt:root:class="bg-white dark:bg-black flex items-center justify-center flex-wrap px-4 py-2 border-0 text-slate-500 dark:text-white/60"
      :rows="size"
      :always-show="false"
      :total-records="total"
      :rows-per-page-options="[10, 20, 30]"
      @page="onUpdatedRows"
    />
  </div>
</template>

<i18n lang="yaml">
en:
  'title': 'Ask doctor'
  'subtitle': 'Receive answer in 24 hours by text or audio'
  'questions:title': 'List of answered questions'
  'btn:load more': 'Load more'
  'btn:see': 'See'
vi:
  'title': 'Hỏi bác sĩ'
  'subtitle': 'Nhận phản hồi trong 24h bằng tin nhắn hoặc audio'
  'questions:title': 'Danh sách câu hỏi đã giải đáp'
  'btn:load more': 'Tải thêm'
  'btn:see': 'Xem'
</i18n>
