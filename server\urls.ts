// server/urls.ts

/**
 * URL configuration for communication between client and nitro server, ensure consistency and ease of maintenance
 */

export const url: Record<string, string> = {
  'content': `/api/elastic-read/search/notion-website/_search`,
  'product': `/api/elastic-read/search/catalog_product/_search`,
  'ratings': `/api/elastic-read/search/notion-ratings/_search`,
  'banners': `/api/elastic-read/search/notion-banners/_search`,
  'ask-doctor': `/api/elastic-read/search/notion-ask-doctor/_search`,
  'ratings-homepage': `/api/elastic-read/search/ratings-homepage/_search`,
}

export default url
