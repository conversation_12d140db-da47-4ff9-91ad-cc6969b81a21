/* tailwind.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Color */
    --primary: 0 150 136;
    --secondary: 255 87 34;
  
    --rating: 253 173 71;
  
    --white: 255 255 255;
    --surface-50: 249 250 251;
    --surface-100: 243 244 246;
    --surface-200: 229 231 235;
    --surface-300: 209 213 219;
    --surface-400: 156 163 175;
    --surface-500: 107 114 128;
    --surface-600: 13 14 20;
    --surface-700: 55 65 81;
    --surface-800: 31 41 55;
    --surface-900: 15 15 15;
    --surface-950: 8 8 8;
    --header-height: 68px;
  }
  
  body {
    font-family: Inter;
    @apply dark:text-surface-50 text-surface-950 bg-white dark:bg-surface-950
  }

  .icon-zalo {
    background-image: url('/icons/zalo.svg');
    width: 30px;
    height: 35px;
    padding: 0px;
    background-repeat: no-repeat;
    /* background-attachment: fixed; */
    background-position: center;
    background-size: contain;
  }

  ::selection {
    @apply bg-primary-100 dark:bg-primary-800
  }
  ::-moz-selection {
    @apply bg-primary-100 dark:bg-primary-800
  }
  ::-webkit-selection {
    @apply bg-primary-100 dark:bg-primary-800
  }
  @media (max-width: 640px) {
    ::-webkit-scrollbar {
      display: none;
    }
  }

  html {
    line-height: 1.5;
  }
  h1 {
    @apply text-gray-900 mb-3 text-2xl font-bold tracking-tight sm:mb-4 sm:text-3xl md:mb-5 md:text-4xl xl:mb-6 xl:text-5xl 2xl:text-6xl dark:text-white;
  }
  h2 {
    @apply text-gray-800 dark:text-gray-100 mb-2 text-3xl font-bold tracking-tight sm:mb-3 md:mb-4 md:text-4xl xl:mb-5;
  }
  h3 {
    @apply text-gray-700 dark:text-gray-200 mb-2 text-2xl font-semibold tracking-tight md:mb-3 md:text-3xl xl:mb-4;
  }
  h4 {
    @apply text-gray-600 dark:text-gray-300 mb-3 text-xl font-semibold tracking-tight md:text-2xl;
  }
}

@layer components {
  .container {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
  }
  .text-7xl {
    line-height: 1.5;
  }

  @media (min-width: 1400px) {
    .container {
      max-width: 1400px;
    }
  }
}

@layer utilities {
  .text-7xl {
    line-height: 1.25;
  }
  .text-6xl {
    line-height: 1.3;
  }
}

li {
  margin-bottom: 8px;
  font-size: 16px; 
}

@media (max-width: 640px) { 
  li {
    font-size: 14px;
  }
}
