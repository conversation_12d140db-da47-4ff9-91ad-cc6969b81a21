<!-- TheLocaleSwitcher.vue -->
<script setup lang="ts">
const { locales, setLocale } = useI18n()
const menu = ref()
const items = [
  {
    items: locales.value.map(i => ({
      label: i.name,
      command: () => setLocale(i.code),
    })),
  },
]
const toggle = (event) => {
  menu.value.toggle(event)
}
</script>

<template>
  <div>
    <Button
      text
      rounded
      icon="pi pi-language"
      aria-haspopup="true"
      aria-controls="overlay_menu"
      @click="toggle"
    />
    <Menu
      id="overlay_menu"
      ref="menu"
      :model="items"
      :popup="true"
    />
  </div>
</template>
