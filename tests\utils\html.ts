// utils/html.ts

export function useHtml(html: string) {
  const getArray = function (reg: RegExp): string[] {
    const output = []
    // const reg = new RegExp(r)
    let match = null
    while ((match = reg.exec(html) !== null)) {
      output.push(match[1])
    }
    return output
  }
  const title = html.match(/<title[^>]*>([^<]+)<\/title>/i)[1]
  const h1s = getArray(/<h1[^>]*>([^<]+)<\/h1>/gi)

  const h2s = getArray(/<h2[^>]*>([^<]+)<\/h2>/gi)
  const h3s = getArray(/<h3[^>]*>([^<]+)<\/h3>/gi)
  const h4s = getArray(/<h4[^>]*>([^<]+)<\/h4>/gi)
  const h5s = getArray(/<h5[^>]*>([^<]+)<\/h5>/gi)
  const h6s = getArray(/<h6[^>]*>([^<]+)<\/h6>/gi)
  const links
    = html.match(/<a[^>]*href=["']([^"']*\/\/[^"']*)["'][^>]*>/gi) || []

  const headLinks = html.match(/<link (.*)>/gi) || []

  const metaDescription
    = html.match(/<meta name="description" content="([^"]+)">/i)?.[1] || ''
  const hreflangs = html.match(/hreflang/g) || []

  const imgSrcs = getArray(/<img[^>]*src=["']([^"']+)["'][^>]*>/gi)

  return {
    h1s,
    h2s,
    h3s,
    h4s,
    h5s,
    h6s,
    headLinks,
    hreflangs,
    imgSrcs,
    links,
    metaDescription,
    title,
  }
}
