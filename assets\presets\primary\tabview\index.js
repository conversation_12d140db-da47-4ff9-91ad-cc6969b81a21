export default {
  navContainer: ({ props }) => ({
    class: [
      // Position
      'relative',
      // Misc
      { 'overflow-hidden': props.scrollable },
    ],
  }),
  navContent: {
    class: [
      // Overflow and Scrolling
      'overflow-y-hidden overscroll-contain',
      'overscroll-auto',
      'scroll-smooth',
      '[&::-webkit-scrollbar]:hidden',
    ],
  },
  previousButton: {
    class: [
      // Flexbox and Alignment
      'flex items-center justify-center',

      // Position
      '!absolute',
      'top-0 left-0',
      'z-20',

      // <PERSON><PERSON> and <PERSON>hape
      'h-full w-12',
      'rounded-none',

      // Colors
      'bg-white dark:bg-surface-800',
      'text-primary dark:text-primary',
      'shadow-md',
    ],
  },
  nextButton: {
    class: [
      // Flexbox and Alignment
      'flex items-center justify-center',

      // Position
      '!absolute',
      'top-0 right-0',
      'z-20',

      // <PERSON><PERSON> and <PERSON>hape
      'h-full w-12',
      'rounded-none',

      // Colors
      'bg-white dark:bg-surface-800',
      'text-primary dark:text-primary',
      'shadow-md',
    ],
  },
  nav: {
    class: [
      // Flexbox
      'flex flex-1',

      // Spacing
      'list-none',
      'p-0 m-0',

      // Colors
      'bg-white dark:bg-surface-800',
      'border-b-2 border-surface-200 dark:border-surface-700',
      'text-surface-900 dark:text-white/80',
    ],
  },
  tabpanel: {
    header: ({ props }) => ({
      class: [
        // Spacing
        'mr-0',

        // Misc
        {
          'opacity-60 cursor-default user-select-none select-none pointer-events-none':
            props?.disabled,
        },
      ],
    }),
    headerAction: ({ parent, context }) => ({
      class: [
        'relative',

        // Font
        'font-bold text-lg',

        // Flexbox and Alignment
        'flex items-center',

        // Spacing
        'py-2 px-3',
        '-mb-[2px]',

        // Shape
        'border-b-2',
        // 'rounded-t-md',

        // Colors and Conditions
        {
          'border-surface-200 dark:border-surface-700':
            parent.state.d_activeIndex !== context.index,
          'bg-white dark:bg-surface-800':
            parent.state.d_activeIndex !== context.index,
          'text-surface-700 dark:text-white/80':
            parent.state.d_activeIndex !== context.index,

          'bg-white dark:bg-surface-800':
            parent.state.d_activeIndex === context.index,
          'border-primary dark:border-primary':
            parent.state.d_activeIndex === context.index,
          'text-primary dark:text-primary':
            parent.state.d_activeIndex === context.index,
        },

        // States
        'focus-visible:outline-none focus-visible:outline-offset-0 focus-visible:ring focus-visible:ring-inset',
        'focus-visible:ring-primary/50 dark:focus-visible:ring-primary/50',
        {
          'hover:bg-white dark:hover:bg-surface-800/80':
            parent.state.d_activeIndex !== context.index,
          'hover:border-surface-400 dark:hover:border-primary':
            parent.state.d_activeIndex !== context.index,
          'hover:text-surface-900 dark:hover:text-white':
            parent.state.d_activeIndex !== context.index,
        },

        // Transitions
        'transition-all duration-200',

        // Misc
        'cursor-pointer select-none text-decoration-none',
        'overflow-hidden',
        'user-select-none',
      ],
    }),
    headerTitle: {
      class: [
        // Text
        'leading-none',
        'whitespace-nowrap',
      ],
    },
    content: {
      class: [
        // Spacing
        'p-2',

        // Shape
        'rounded-b-md',

        // Colors
        'bg-white dark:bg-surface-800',
        'text-surface-700 dark:text-white/80',
        'border-0',
      ],
    },
  },
}
