<script setup lang="ts">
import TimelineItem from './Item/Layout/timeline.vue'
import TimeLineSkeleton from './Item/Sketeton/timeline.vue'

type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
interface Props {
  articles: any[]
  slug: string
  blocks: BlockType[]
  loading: boolean
}
defineProps<Props>()
</script>

<template>
  <Timeline
    :value="loading ? [1, 1, 1] : articles"
    :pt="{
      root: 'container py-4 px-3 md:px-0 mt-[30px]',
      opposite: 'w-0',
      content:
        'relative w-full sm:w-[620px] md:w-[750px] lg:w-[1000px] sm:ml-10 p-2',
      event: 'flex justify-around sm:justify-center',
    }"
  >
    <template #content="slotProps">
      <TimeLineSkeleton v-if="loading" />
      <TimelineItem
        v-else
        :article="slotProps"
        :slug="slug"
      />
    </template>
  </Timeline>
</template>
