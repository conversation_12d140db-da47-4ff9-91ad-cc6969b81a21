import { ref, onMounted, onUnmounted } from '#imports'

export function useScrollProgress() {
  const progress = ref(0)

  const updateScrollProgress = () => {
    const totalHeight
      = document.documentElement.scrollHeight - window.innerHeight
    const scrolled = window.scrollY
    const calculatedProgress = (scrolled / totalHeight) * 100
    progress.value = Math.min(calculatedProgress, 100)
  }

  onMounted(() => {
    window.addEventListener('scroll', updateScrollProgress)
  })

  onUnmounted(() => {
    window.removeEventListener('scroll', updateScrollProgress)
  })

  return { progress }
}
