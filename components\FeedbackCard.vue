<template>
  <div
    :class="[
      'min-h-40 w-full rounded-md p-4 text-start',
      props.pt?.item,
    ]"
    style="
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
          "
  >
    <div :class="['mb-2 flex flex-row items-center', props.pt?.itemHeader]">
      <WAvatar
        class="mr-2"
        :gender="props.rating.authorGender"
        :url="props.rating.avatar"
      />
      <div>
        <div
          :class="[
            'mb-[3px] flex items-center justify-between gap-1.5 text-sm sm:text-base',
            props.pt?.commentHeader,
          ]"
        >
          <span
            :class="[
              'text-surface-700 font-semibold dark:text-white',
              props.pt?.commentName,
            ]"
          >
            {{ props.rating.authorName }}
          </span>
          <div class="w-1 h-1 rounded-full bg-gray-300"></div>
          <span
            :class="[
              'text-surface-400 text-xs sm:text-sm',
              props.pt?.commentTime,
            ]"
          >
            {{ dateFromNow }}
          </span>
        </div>
        <Rating
          v-model="stars"
          readonly
          :cancel="false"
        />
      </div>
    </div>
    <blockquote :class="['text-sm sm:text-base', props.pt?.commentContent]">
      {{
        props.rating.halfComment
          ? isShowMore
            ? props.rating.fullComment
            : props.rating.halfComment
          : props.rating.fullComment
      }}
    </blockquote>
    <div class="flex justify-end">
      <Button
        v-if="props.rating.isOverlap"
        plain
        text
        size="small"
        class="mt-2"
        @click="toggleShowMore()"
      >
        {{ `${t('show')} ${isShowMore ? t('less') : t('more')}` }}
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDateDiffWithLocale } from '~/composables/component/feedback-card'

export interface IRating {
  halfComment?: string
  fullComment: string
  stars: number
  authorName: string
  avatar?: string
  authorGender: string
  isOverlap?: boolean
  dateCreated: string
}

interface Props {
  rating: IRating
  pt?: any
  index: number
}

const { t } = useI18n({ useScope: 'local' })

const props = defineProps<Props>()

const showMoreIndex = defineModel<number>('showMoreIndex')

const isShowMore = computed(() => showMoreIndex.value == props.index)

const dateFromNow = useDateDiffWithLocale(props.rating.dateCreated)

const stars: Ref<number> = ref(props.rating.stars)

// methods
const toggleShowMore = () => {
  if (showMoreIndex.value == -1) showMoreIndex.value = props.index
  else if (showMoreIndex.value == props.index) showMoreIndex.value = -1
}
</script>

<i18n lang="yaml">
en:
  'see-all-feedbacks': 'See 1K+ feedbacks'
  'less': 'less'
  'more': 'more'
  'show': 'Show'
vi:
  'see-all-feedbacks': 'Xem 1K+ lời bình'
  'less': 'ít hơn'
  'more': 'thêm'
  'show': 'Hiển thị'
</i18n>
