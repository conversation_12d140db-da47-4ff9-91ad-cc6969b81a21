<script setup lang="ts">
interface Media {
  src: string
  type: string
  alt?: string
}
interface Feature {
  image?: Media
  video?: Media
  icon?: string
  title?: string
  subTitle?: string
  name?: string
}

const { display } = useDisplay()
const isMobile = computed(() => display.breakpoint.isMobile)

const { features } = defineProps({
  title: {
    type: String,
    required: false,
    default: '',
  },
  subtitle: {
    type: String,
    required: false,
    default: '',
  },
  features: {
    type: Array as PropType<Feature[]>,
    default: () => [],
  },
})

const videoIndex = ref(0)

const onNextVideo = (index: number) => {
  videoIndex.value = index
}
</script>

<template>
  <section class="flex flex-col gap-8 px-4 py-12">
    <div class="text-center">
      <h2>
        {{ title }}
      </h2>
      <h5>
        {{ subtitle }}
      </h5>
    </div>
    <div
      class="flex flex-col justify-center gap-8 align-middle sm:flex-row sm:gap-24"
    >
      <BlockDeviceMockupPhone v-if="features[videoIndex]?.video?.src">
        <template #image>
          <ClientOnly>
            <WVideo
              :key="videoIndex"
              :sources="[features[videoIndex]?.video]"
              :video-name="features[videoIndex]?.name"
              class="video-js vjs-default-skin vjs-big-play-centered h-[600px] w-full transition-all"
              native-controls-for-touch
              autoplay="true"
              controls="true"
              control-bar="false"
              poster="/images/wellcare-splash.png"
              native-audio-tracks
              native-text-track
              playsinline
            />
          </ClientOnly>
        </template>
      </BlockDeviceMockupPhone>

      <Carousel
        v-if="isMobile"
        :value="features"
        :num-visible="1"
        :num-scroll="1"
        class="sm:hidden"
        @update:page="onNextVideo"
      >
        <template #item="slotProps">
          <div
            class="px-auto flex w-full snap-center flex-col place-content-center text-center sm:w-1/4 sm:flex-grow"
          >
            <span class="text-3xl font-bold">
              {{ features[slotProps.index].title }}
            </span>
            {{ features[slotProps.index].subTitle }}
          </div>
        </template>
      </Carousel>

      <div
        v-else
        class="flex flex-col justify-center gap-12 align-top"
      >
        <div
          v-for="(item, index) in features"
          :key="index"
          :class="
            'flex w-full origin-left cursor-pointer flex-col border-l-4 pl-2 transition-transform '
              + (videoIndex == index
                ? 'text-primary border-primary scale-105'
                : 'text-slate-400 dark:text-slate-300')
          "
          @click="onNextVideo(index)"
        >
          <span class="text-3xl font-bold">
            {{ item.title }}
          </span>
          {{ item.subTitle }}
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.video-js :deep(button.vjs-big-play-button) {
  height: 70px;
  width: 70px;
  border-radius: 100%;
  transform: translateX(10px);
}
.video-js :deep(span.vjs-icon-placeholder::before) {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: auto;
  height: auto;
}
.video-js :deep(.vjs-play-progress.vjs-slider-bar) {
  background-color: var(--v-primary-base);
}
</style>
