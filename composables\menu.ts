// composables/menu.ts

import type { ComputedRef } from '#imports'
import type { MenuItem } from '~/models'

export function useMegaMenu(menu: ComputedRef<MenuItem[]>) {
  const panel = computed(() => {
    return menu.value.map(i => ({
      ...i,
      items: i.items?.map(x => x[0]),
    }))
  })
  return { panel }
}
const usePrimeVueMenu = function (
  menu: ComputedRef<MenuItem[][] | MenuItem[]>,
) {
  const localePath = useLocalePath()
  const { push } = useRouter()
  const addCommand = function (item: MenuItem) {
    if (item.route && !item.command) {
      item.command = () => push(localePath(item.route))
    }
    return item
  }
  const processMenu = function (menu: MenuItem[] | MenuItem[][]) {
    menu.forEach((submenu: MenuItem | MenuItem[]) => {
      if (!Array.isArray(submenu)) {
        submenu = addCommand(submenu)
        if (submenu.items) {
          submenu.items = processMenu(submenu.items)
        }
      }
      else {
        submenu = submenu.map(i => processMenu([i])) as any
      }
    })
    return menu
  }

  const output = computed(() => processMenu(menu.value))
  return output
}

export function useMenu() {
  const { t } = useI18n({ useScope: 'global' })

  const top = computed(() => [
    {
      label: t('menu:home'),
      route: '/',
      root: true,
    },
    {
      label: t('menu:dich-vu'),
      items: [
        [
          {
            label: t('menu:dich-vu:dich-vu-wellcare'),
            route: '#',
            items: [
              {
                label: t('menu:dich-vu:kham-tu-xa'),
                route: '/dich-vu/kham-tu-xa',
                // subtext: t('menu:dich-vu:kham-tu-xa:subtext'),
              },
              {
                label: t('menu:dich-vu:tu-van-tri-lieu-tam-ly'),
                route: '/dich-vu/tu-van-tri-lieu-tam-ly',
                // subtext: t('menu:dich-vu:tu-van-tri-lieu-tam-ly:subtext'),
              },
              {
                label: t('menu:dich-vu:giai-dap-suc-khoe'),
                route: '/dich-vu/giai-dap-suc-khoe',
                // subtext: t('menu:dich-vu:giai-dap-suc-khoe:subtext'),
              },

              {
                label: t('menu:dich-vu:bac-si-rieng'),
                route: '/dich-vu/bac-si-rieng',
                // subtext: t('menu:dich-vu:bac-si-rieng:subtext'),
              },
              {
                label: t('menu:dich-vu:y-kien-doc-lap'),
                route: '/dich-vu/y-kien-doc-lap',
                // subtext: t('menu:dich-vu:y-kien-doc-lap:subtext'),
              },
              {
                label: t('menu:dich-vu:healthgpt'),
                route: '/dich-vu/healthgpt',
                // subtext: t('menu:dich-vu:healthgpt:subtext'),
              },
              {
                label: t('menu:dich-vu:eduhub'),
                route: '/dich-vu/edu-hub-kho-tri-thuc-suc-khoe',
                // subtext: t('menu:dich-vu:eduhub:subtext'),
              },
              {
                label: t('menu:dich-vu:chuong-trinh-suc-khoe'),
                route: '/dich-vu/chuong-trinh-suc-khoe',
                // subtext: t('menu:dich-vu:chuong-trinh-suc-khoe:subtext'),
              },
            ],
          },
        ],
        [
          {
            label: t('menu:dich-vu:membership'),
            route: '#',
            items: [
              {
                label: t('menu:dich-vu:subscription'),
                route: '/dich-vu/dang-ky-thanh-vien',
                // subtext: t('menu:dich-vu:subscription:subtext'),
              },
              {
                label: t('menu:dich-vu:doanh-nghiep'),
                route: '/doanh-nghiep',
                // subtext: t('menu:dich-vu:doanh-nghiep:subtext'),
              },
            ],
          },
        ],
      ],
      root: true,
    },
    {
      label: t('menu:solutions'),
      root: true,
      items: [
        [
          {
            label: t('menu:danh-cho-doanh-nghiep'),
            items: [
              {
                label: t('menu:phuc-loi-kham-tu-xa-cho-nhan-vien'),
                route: '/doanh-nghiep/phuc-loi-kham-tu-xa-cho-nhan-vien',
              },
              {
                label: t('menu:chuong-trinh-ho-tro-tam-ly-eap'),
                route: '/doanh-nghiep/chuong-trinh-ho-tro-tam-ly-eap',
              },
              {
                label: t('menu:tam-soat-suc-khoe-tinh-than'),
                route: '/doanh-nghiep/tam-soat-suc-khoe-tinh-than',
              },
              {
                label: t('menu:healthtalk'),
                route: '/doanh-nghiep/healthtalk',
              },
              // {
              //   label: t('menu:goi-doanh-nghiep'),
              //   route: '/doanh-nghiep/goi-doanh-nghiep',
              // },
            ],
          },
        ],
        [
          {
            label: t('menu:danh-cho-doi-tac'),
            items: [
              {
                label: t('menu:benefit-for-customers'),
                route: '/doi-tac/mon-qua-suc-khoe',
              },
              {
                label: t('menu:app-integration'),
                route: '/doi-tac/tich-hop-ung-dung',
              },
            ],
          },
        ],
      ],
    },
  ])

  const sitemapLinks: ComputedRef<MenuItem[]> = computed(() => [
    {
      label: t('menu:danh-cho-doanh-nghiep'),
      route: '#',
      items: [
        {
          label: t('menu:phuc-loi-kham-tu-xa-cho-nhan-vien'),
          route: '/doanh-nghiep/phuc-loi-kham-tu-xa-cho-nhan-vien',
        },
        {
          label: t('menu:chuong-trinh-ho-tro-tam-ly-eap'),
          route: '/doanh-nghiep/chuong-trinh-ho-tro-tam-ly-eap',
        },
        {
          label: t('menu:tam-soat-suc-khoe-tinh-than'),
          route: '/doanh-nghiep/tam-soat-suc-khoe-tinh-than',
        },
        {
          label: t('menu:healthtalk'),
          route: '/doanh-nghiep/healthtalk',
        },
      ],
    },
    {
      label: t('menu:danh-cho-benh-nhan'),
      route: '#',
      items: [
        {
          label: t('menu:dich-vu'),
          route: '/dich-vu',
        },
        {
          label: t('menu:chuong-trinh-thanh-vien'),
          route: '/dich-vu/dang-ky-thanh-vien',
        },
        {
          label: t('menu:blog-song-khoe'),
          route: '/song-khoe',
        },
        {
          label: t('menu:cam-nang'),
          route: '/cam-nang',
        },
        {
          label: t('menu:hoi-bac-si'),
          route: '/hoi-bac-si',
        },
      ],
    },
    {
      label: t('menu:ho-tro'),
      route: '#',
      items: [
        {
          label: t('menu:cau-hoi-thuong-gap'),
          route: '/ho-tro/cau-hoi-thuong-gap',
        },
        {
          label: t('menu:lien-he-chung-toi'),
          route: '/lien-he',
        },
        {
          label: t('menu:chinh-sach-bao-mat'),
          route: '/ho-tro/chinh-sach-bao-mat',
        },
        {
          label: t('menu:dieu-khoan-su-dung'),
          route: '/ho-tro/dieu-khoan-su-dung',
        },
      ],
    },
    {
      label: t('menu:doi-net-ve-chung-toi'),
      route: '#',
      items: [
        {
          label: t('menu:tin-tuc'),
          route: '/tin-tuc',
        },
        {
          label: t('menu:truyen-thong'),
          route: '/gioi-thieu/truyen-thong',
        },
        {
          label: t('menu:khach-hang-noi-ve-chung-toi'),
          route: '/gioi-thieu/khach-hang-noi-ve-chung-toi',
        },
        {
          label: t('menu:nha-dau-tu'),
          route: '/gioi-thieu/nha-dau-tu',
        },
      ],
    },
  ])

  const socialLinks: ComputedRef<MenuItem[]> = computed(() => [
    {
      icon: 'pi pi-facebook',
      label: 'facebook',
      route: 'https://www.facebook.com/wellcare.vn/',
    },
    {
      icon: 'pi pi-youtube',
      label: 'youtube',
      route: 'https://www.youtube.com/@WellcareVn',
    },
    {
      icon: 'icon-zalo',
      label: 'zalo',
      route: 'https://zalo.me/2727084330920973261',
    },
    {
      label: 'linkedin',
      icon: 'pi pi-linkedin',
      route: 'https://www.linkedin.com/company/mhealth-technologies-jsc',
    },
  ])

  const privacy = computed(() => [
    {
      label: t('menu:about-us'),
      route: '/about-us',
    },
    {
      label: t('menu:terms-and-conditions'),
      route: '/terms-and-conditions',
    },
    {
      label: t('menu:privacy'),
      route: '/privacy',
    },
  ])

  const appstores = computed(() => [
    {
      icon: '/images/download/en-app-store-badge.svg',
      label: 'AppStore',
      route: 'https://apps.apple.com/us/app/wellcare/id1039423586',
    },
    {
      icon: '/images/download/en-google-play-badge.png',
      label: 'Playstore',
      route: 'https://play.google.com/store/apps/details?id=vn.wellcare',
    },
  ])

  const contacts = computed((): MenuItem[] => [
    {
      icon: 'pi pi-phone',
      label: '(+84) 28 3622 6822',
      command: () => {
        window.location.href = 'tel:+842836226822'
      },
    },
    {
      icon: 'pi pi-envelope',
      label: '<EMAIL>',
      command: () => {
        window.location.href = 'mailto:<EMAIL>'
      },
    },
    {
      icon: 'pi pi-map-marker',
      label:
        'LA0208 Lexington Office, 67 Mai Chi Tho, An Phu Ward, Ho Chi Minh City, Vietnam',
    },
  ])

  return {
    // top: generateCommandForRoutes(top.value),
    top: usePrimeVueMenu(top),
    appstores: usePrimeVueMenu(appstores),
    sitemapLinks: usePrimeVueMenu(sitemapLinks),
    privacy: usePrimeVueMenu(privacy),
    socialLinks: usePrimeVueMenu(socialLinks),
    contacts: usePrimeVueMenu(contacts),
  }
}
