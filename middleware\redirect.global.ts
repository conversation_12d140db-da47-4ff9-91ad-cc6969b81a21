// middleware/redirect.global.ts
export default defineNuxtRouteMiddleware((to) => {
  const config = useRuntimeConfig()

  const defaultAllowedPaths = [
    '/',
    '/ho-tro/chinh-sach-bao-mat',
    '/ho-tro/dieu-khoan-su-dung',
    '/ho-tro/cau-hoi-thuong-gap',
  ]

  const envAllowedPaths = config.public && config.public.allowedPaths && Array.isArray(config.public.allowedPaths)
    ? config.public.allowedPaths.map((path: string) => path.trim())
    : []

  const allowedPaths = envAllowedPaths.length > 0
    ? envAllowedPaths
    : defaultAllowedPaths

  if (!allowedPaths.includes(to.path)) {
    return navigateTo('/')
  }
})
