<template>
  <div
    class="relative mx-auto h-[600px] w-[300px] rounded-xl border-[14px] border-gray-800 bg-gray-800 shadow-xl dark:border-gray-800"
  >
    <div
      class="w-[148px] h-[18px] bg-gray-800 top-0 rounded-b-[1rem] left-1/2 -translate-x-1/2 absolute"
    ></div>
    <div
      class="h-[32px] w-[3px] bg-gray-800 absolute -start-[17px] top-[72px] rounded-s-lg"
    ></div>
    <div
      class="h-[46px] w-[3px] bg-gray-800 absolute -start-[17px] top-[124px] rounded-s-lg"
    ></div>
    <div
      class="h-[46px] w-[3px] bg-gray-800 absolute -start-[17px] top-[178px] rounded-s-lg"
    ></div>
    <div
      class="h-[64px] w-[3px] bg-gray-800 absolute -end-[17px] top-[142px] rounded-e-lg"
    ></div>
    <div
      class="h-[572px] w-[272px] overflow-hidden rounded-xl bg-white dark:bg-gray-800"
    >
      <slot name="image"></slot>
    </div>
  </div>
</template>
