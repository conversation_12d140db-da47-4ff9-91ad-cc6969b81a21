import { useNuxtApp } from '#imports'
import type { HitNotionWebsite, ResponseElasticSearch } from '~/models'
import { ElasticIndex } from '~/models'
import { useFetchDefault } from '~/composables/fetch'

export function usePageGrandCategory(
  slug: string | ComputedRef<string> | Ref<string>,
) {
  const { t } = useI18n()
  const { query } = useRoute()
  const { $fetchWellcare } = useNuxtApp()

  const { filter, should, sort, _source } = useFetchDefault(
    ElasticIndex.NOTION_WEBSITE,
  )

  const pageSize = ref(50)
  const computedSlug = isRef(slug) ? slug : computed(() => slug)

  const _page = parseInt(query.page as string)
  const currentPage = ref(_page >= 1 ? _page : 1)

  watch(currentPage, (newValue) => {
    if (newValue > 1) navigateTo({ query: { page: newValue } })
    else navigateTo({ query: {} })
  })

  const { data: category, status } = useAsyncData<any>(
    computedSlug.value,
    () =>
      $fetchWellcare(ElasticIndex.NOTION_WEBSITE, {
        method: 'post',
        body: {
          size: 1,
          from: 0,
          query: {
            bool: {
              must: [
                {
                  term: {
                    'page.properties.Slug.keyword': computedSlug.value,
                  },
                },
                {
                  term: {
                    'page.properties.Type.keyword': 'Category',
                  },
                },
              ],
              filter,
              should,
            },
          },
          sort,
          // _source: {
          //   includes: ['page', 'blockstring'],
          // },
        },
      }),
    {
      transform: transform.hit,
      watch: [pageSize, currentPage],
    },
  )

  const checkError = () => {
    if (
      status.value === 'error'
      || (status.value === 'success' && !category.value['_id'])
    ) {
      showError({
        statusCode: 404,
        statusMessage: t('error:page-not-found:message'),
        cause: t('error:page-not-found:cause'),
        fatal: true,
      })
    }
  }

  watch(status, checkError)

  const title = computed(() => {
    return (
      category.value?.page.properties.Name
      + (_page > 1 ? ` - ${t('pagination:page')} ${_page}` : '')
    )
  })

  const description = computed(() => {
    return (
      category.value?.page.properties['Meta Description']
      || category.value?.page.properties.Sapo
      + (_page > 1 ? ` - ${t('pagination:page')} ${_page}` : '')
    )
  })

  useSeoMeta({
    title,
    ogTitle: category.value?.page.properties.Name,
    description: description.value,
    ogDescription: description.value,
    ogLocale: category.value?.page.properties.Locale,
  })

  const { data: subCategories, status: statusSubCategories }
    = useAsyncData<any>(
      () => {
        const categories = <Promise<ResponseElasticSearch<HitNotionWebsite>>>(
          $fetchWellcare(ElasticIndex.NOTION_WEBSITE, {
            method: 'post',
            body: {
              from: pageSize.value * (currentPage.value - 1),
              size: pageSize.value,
              _source,
              query: {
                bool: {
                  must: [
                    {
                      term: {
                        'page.properties.Parent item.properties.Slug.keyword':
                          computedSlug.value,
                      },
                    },
                    {
                      term: {
                        'page.properties.Type.keyword': 'Category',
                      },
                    },
                  ],
                  filter,
                  should,
                },
              },
              sort,
            },
          })
        )

        return new Promise((resolve, reject) => {
          categories
            .then((categoriesData) => {
              const subCategories = categoriesData.body.hits.hits
                .map(i => i.page.properties.Slug)
                .filter(i => i)
              return $fetchWellcare<ResponseElasticSearch<HitNotionWebsite>>(
                ElasticIndex.NOTION_WEBSITE,
                {
                  method: 'post',
                  body: {
                    from: 0,
                    size: 100,
                    _source,
                    query: {
                      bool: {
                        filter: [
                          {
                            bool: {
                              minimum_should_match: 1,
                              should: subCategories.map(i => ({
                                match_phrase: {
                                  'page.properties.Parent item.properties.Slug.keyword':
                                    i,
                                },
                              })),
                            },
                          },
                        ],
                        must: filter,
                        should,
                      },
                    },
                    collapse: {
                      field:
                        'page.properties.Parent item.properties.Slug.keyword',
                      inner_hits: {
                        name: 'most_recent',
                        size: 5,
                        from: 0,
                        sort,
                        _source,
                      },
                      max_concurrent_group_searches: 4,
                    },
                    sort,
                  },
                },
              )
                .then((collapseArticlesData: any) => {
                  // Once both promises are resolved, resolve with categories data
                  categoriesData.body.hits.hits
                    = categoriesData.body.hits.hits.map((hit) => {
                      hit.inner_hits = collapseArticlesData.body.hits.hits.find(
                        i =>
                          i.fields[
                            'page.properties.Parent item.properties.Slug.keyword'
                          ].includes(hit.page.properties.Slug),
                      )?.inner_hits
                      return hit
                    })
                  resolve(categoriesData)
                })
                .catch((error: Error) => {
                  console.error(error)
                  reject(error) // Reject if collapseArticles promise fails
                })
            })
            .catch((error) => {
              console.error(error)
              reject(error) // Reject if categories promise fails
            })
        })
      },
      {
        transform: transform.hits,
        watch: [currentPage, pageSize],
      },
    )

  const loading = computed<boolean>(
    () => status.value === 'pending' || statusSubCategories.value === 'pending',
  )

  return {
    title,
    category,
    currentPage,
    description,
    loading,
    subCategories,
    pageSize,
  }
}
