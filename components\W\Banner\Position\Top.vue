<script setup lang="ts">
import type { PropType } from 'vue'
import type { ComponentBanner } from '~/models'

const { banner } = defineProps({
  banner: {
    type: Object as PropType<ComponentBanner>,
    default: () => {},
  },
})
defineEmits(['click'])
</script>

<template>
  <NuxtImg
    v-if="banner?.source"
    :key="banner?.source"
    :src="banner?.source"
    preset="thumbnail"
    width="800px"
    format="webp"
    sizes="sm:480px md:600px lg:800px"
    class="w-full cursor-pointer rounded-xl blinking"
    @click="$emit('click')"
  />
</template>
