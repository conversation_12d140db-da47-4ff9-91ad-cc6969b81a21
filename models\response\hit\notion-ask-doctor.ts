import type { HitNotion, NotionDate, NotionPage } from './notion'
import type { NotionProviderPageProperties } from './notion-providers'
import type { IEmbed } from '~/models/transform/notion-block'

export interface NotionAskDoctorPageProperties {
  '_id'?: string
  'note'?: string
  'EduHub'?: any
  'Type'?: string
  'Status'?: string
  'Select'?: string
  'Tags'?: string[]
  'Files'?: IEmbed[]
  'Question'?: string
  'Description'?: string
  'Date Created'?: string
  'chiefComplaint'?: string
  'provider._id'?: string
  'ModifiedAt'?: NotionDate
  'provider.name'?: string
  'Last Updated?'?: string
  'PublishedAt'?: NotionDate
  'Providers'?: NotionPage<NotionProviderPageProperties>
  'AnsweredBy'?: NotionPage<NotionProviderPageProperties>
  'Sub-item'?: NotionPage<NotionAskDoctorPageProperties>[]
  'Parent item'?: NotionPage<NotionAskDoctorPageProperties>[]
}

export interface HitNotionAskDoctor extends HitNotion {
  page?: NotionPage<NotionAskDoctorPageProperties>
}
