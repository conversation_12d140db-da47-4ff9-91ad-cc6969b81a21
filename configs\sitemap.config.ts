import type { ModuleOptions } from '@nuxtjs/sitemap'

export const sitemap: Partial<ModuleOptions> = {
  debug: true,
  sitemaps: {
    'static': {
      includeAppSources: true,
      include: ['/**'],
      exclude: ['/admin/**', '/en/admin/**'],
    },
    // ['cam-nang']: {
    //   sources: ['/api/__sitemap__/cam-nang'],
    // },
    // ['dich-vu']: {
    //   sources: ['/api/__sitemap__/dich-vu'],
    // },
    // ['doanh-nghiep']: {
    //   sources: ['/api/__sitemap__/doanh-nghiep'],
    // },
    // ['doi-tac']: {
    //   sources: ['/api/__sitemap__/doi-tac'],
    // },
    // ['gioi-thieu']: {
    //   sources: ['/api/__sitemap__/gioi-thieu'],
    // },
    // ['ho-tro']: {
    //   sources: ['/api/__sitemap__/ho-tro'],
    // },
    // ['song-khoe']: {
    //   sources: ['/api/__sitemap__/song-khoe'],
    // },
    // ['tin-tuc']: {
    //   sources: ['/api/__sitemap__/tin-tuc'],
    // },
    'category': {
      sources: ['/api/__sitemap__/Category?size=100'],
    },
    'article-0-10k': {
      sources: ['/api/__sitemap__/Article?size=10000'],
    },
  },
}
