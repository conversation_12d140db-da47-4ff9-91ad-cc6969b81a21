FROM mhealthvn/node-builder:master as builder
ARG GIT_TOKEN
ENV GIT_TOKEN=$GIT_TOKEN
WORKDIR /usr/src/app
COPY . .
RUN pnpm install
ARG FIRE_ENV
ENV FIRE_ENV=$FIRE_ENV
RUN pnpm build

FROM node:18.19-alpine as runner
WORKDIR /usr/src/app
COPY --from=builder /usr/src/app/.output .output 
RUN echo "{\"tag\": \"$BUILD_TAG\", \"date\": \"$(date '+%F %T%z')\", \"branch\": \"$GIT_BRANCH\", \"commit\": \"$GIT_COMMIT\"}" > .output/public/version.json
USER 1
EXPOSE 3000
CMD ["node", ".output/server/index.mjs"]