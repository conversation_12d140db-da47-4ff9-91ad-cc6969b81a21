<script setup lang="ts">
const isOpen = useAnnouncement()
</script>

<template>
  <div class="sticky top-0 z-50 flex-nowrap justify-center align-middle">
    <div
      class="flex justify-center px-8 py-2"
      style="background: -webkit-gradient(linear, left top, right top, from(#ff66b0), color-stop(#fab13f), to(#0ed294));
    background: -o-linear-gradient(left, #ff66b0, #fab13f, #0ed294);
    background: linear-gradient(to right, #ff66b0, #fab13f, #0ed294);
    "
    >
      <div
        class="text-sm text-white uppercase font-semibold flex items-start gap-1 cursor-pointer"
        @click="isOpen = true"
      >
        <img
          src="../../assets/icons/megaphone.png"
          class="w-[18px] mr-1 object-cover"
        />
        <p>
          <span>Thông báo quan trọng từ Wellcare </span>
          <iconify-icon
            icon="ci:arrow-right-lg"
            class="font-bold"
          />
        </p>
      </div>
    </div>
    <div class="flex justify-center px-8 py-1 bg-white/85">
      <nuxt-link
        to="/"
        class="flex rounded-lg py-2 items-center"
      >
        <NuxtImg
          src="/images/logo.svg"
          alt="Logo"
          class="mx-2 size-10"
        />
        <div class="font-semibold mx-3.5 text-lg">Khám từ xa Wellcare</div>
      </nuxt-link>
    </div>
  </div>
</template>
