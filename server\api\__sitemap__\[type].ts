function getSlugData(type: string, from: number, size: number) {
  const config = useRuntimeConfig()
  const site = config.public.page.site
  const baseUrl = config.public['nuxt3-module-data-layer'].baseUrl
  return new Promise((resolve) => {
    $fetch('/elastic-read/search/notion-website/_search', {
      method: 'POST',
      body: {
        from: from,
        size: size,
        _source: {
          includes: [
            'page.properties.Slug',
            'page.properties.Last edited time',
            'page.properties.Parent item.properties.Slug',
            'page.properties.Site',
          ],
        },
        query: {
          bool: {
            filter: [
              {
                term: {
                  'page.properties.Type.keyword': type,
                },
              },
              {
                term: {
                  'page.properties.Site.keyword': site,
                },
              },
            ],
            must: {
              bool: {
                should: [
                  {
                    term: {
                      'page.properties.Status.keyword': 'Published',
                    },
                  },
                  {
                    term: {
                      'page.properties.Status.keyword': 'reindex',
                    },
                  },
                ],
              },
            },
          },
        },
      },
      retry: 0,
      baseURL: baseUrl,
    })
      .then((res: any) => {
        resolve(res.body)
      })
      .catch((e) => {
        console.error(e)
        resolve(null)
      })
  })
}

type IType = 'Category' | 'Article' | 'Person'

const ParentSlugMapping = {
  Category: '',
  Article: '',
  Person: '/bac-si',
}

export default defineSitemapEventHandler(async (e) => {
  const type: IType = e.context?.params?.type as IType
  const query = getQuery(e)
  const from: number = parseInt((query?.from as string) ?? 0)
  const size: number = parseInt((query?.size as string) ?? 100)
  const result: any = await getSlugData(type, from, size)

  if (!result) return []

  const data: any[] = []
  try {
    if (Array.isArray(result.hits.hits)) {
      for (const hit of result.hits.hits) {
        const pageProperties = hit.page.properties
        // const parentSlug = ''
        // if (
        //   Array.isArray(pageProperties['Parent item']) &&
        //   pageProperties['Parent item'].length > 0
        // ) {
        //   parentSlug = pageProperties['Parent item'][0].properties.Slug
        // }
        const currSlug = pageProperties.Slug
        const loc = `${ParentSlugMapping[type]}/${currSlug}`
        data.push({
          loc,
          lastmod: pageProperties['Last edited time'],
        })
      }
    }
  }
  catch (error) {
    console.error(error)
  }
  return data
})
