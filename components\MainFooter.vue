<script setup lang="ts">
import type { MenuItem } from '~/models'

const { t } = useI18n()
const { sitemapLinks, socialLinks, contacts } = useMenu()

const thisYear = new Date().getFullYear().toString()

export type BlockType =
  | 'copyright'
  | 'brand'
  | 'brand-social-links'
  | 'sitemap'
  | 'appstores'
  | 'contacts'
  | 'certificates'

const { year } = defineProps({
  year: {
    type: String,
    default: '',
  },
  brandName: {
    type: String,
    default: '',
  },
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => [
      'copyright',
      'brand',
      'brand-social-links',
      'sitemap',
      'appstores',
      'contacts',
      'certificates',
    ],
  },
})

// const { sprites } = useImageSprite(
//   'https://storage.googleapis.com/cms-gallery-sandbox/664c54a6562b0169fc03f09b/app.png',
//   { rows: 2, cols: 1 },
//   { width: 120, height: 72 },
// )
</script>

<template>
  <div class="bg-surface-50 dark:bg-surface-900 flex flex-col">
    <div class="px-4 pt-8 md:px-8 lg:px-24">
      <div class="flex flex-col md:flex-row-reverse">
        <!-- Sitemap -->
        <div
          class="flex shrink-0 grow flex-col gap-2 md:grid md:grid-cols-2 lg:flex lg:flex-row lg:justify-between"
        >
          <div
            v-for="(block, index) in sitemapLinks"
            :key="index"
            class="flex flex-col text-left"
          >
            <h6 class="mb-1 font-medium">
              {{ (block as MenuItem).label }}
            </h6>
            <NuxtLinkLocale
              v-for="item in (block as MenuItem).items"
              :key="item?.label"
              :to="item.route"
            >
              <span
                class="text-surface-600 dark:text-surface-400 dark:hover:text-primary hover:text-primary my-2 text-sm decoration-2 hover:underline"
              >
                {{ item.label }}
              </span>
            </NuxtLinkLocale>
          </div>
        </div>
        <Divider
          layout="vertical"
          class="hidden md:block"
        />
        <Divider
          layout="horizontal"
          class="block md:hidden"
        />
        <!-- The Company -->
        <div class="md:max-w-sm">
          <!-- Company Badge and description -->
          <WellcareBadge
            class="mb-2"
            :blocks="['brand-tagline', 'brand-description']"
          />
          <!-- Company Info -->
          <slot
            v-if="blocks.includes('contacts')"
            name="contacts"
          >
            <h6 class="mt-4 font-medium">
              {{ t('company:Info') }}
            </h6>
            <div class="flex flex-col">
              <span
                v-for="(link, index) in contacts"
                :id="`link_contact_${link.label?.includes('@') ? 'mailto' : 'tel'}`"
                :key="index"
                class="text-surface-600 dark:text-surface-300 my-1 text-sm"
                @click="link.command()"
              >
                {{ link.label }}
              </span>
            </div>
          </slot>
        </div>
      </div>
      <Divider layout="horizontal" />
      <!-- Social Media and Download -->
      <div class="flex flex-row flex-wrap justify-between">
        <!-- Download App Link -->
        <div>
          <h6 class="mb-2 mt-4 font-medium">
            {{ t('Download the app') }}
          </h6>
          <PlatformDownload
            class="mt-1 flex items-center justify-between gap-2"
            :width="120"
            :height="72"
            platform-class="mx-auto"
          />
        </div>
        <!-- Social Media Links -->
        <div>
          <h6 class="mt-4 font-medium">
            {{ t('Follow:us') }}
          </h6>
          <div class="grid grid-cols-4 justify-items-center gap-2">
            <NuxtLink
              v-for="(socialMedia, index) in socialLinks"
              :key="index"
              :to="socialMedia.route"
              target="_blank"
              class="flex items-center"
            >
              <Button
                text
                plain
                class="max-h-10 max-w-10 !p-2 !text-xl"
                :icon="socialMedia.icon"
                :aria-label="socialMedia.label"
              />
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
    <!-- Copyright -->
    <div
      class="bg-surface-100 dark:bg-surface-800 mt-4 p-2 text-center md:mt-8"
    >
      <span class="text-sm text-gray-500 dark:text-gray-400">© {{ year }} - {{ thisYear }} • {{ brandName }} • All Rights Reserved
      </span>
    </div>
  </div>
</template>

<i18n lang="yaml">
vi:
  'brand:description': 'Chúng tôi giúp bạn duy trì một lối sống lành mạnh, và khi bạn cần tham vấn y tế, chúng tôi kết nối bạn với những bác sĩ chuyên khoa hàng đầu qua gọi thoại và gọi video.'
  'company:Info': 'Thông tin công ty'
  'Follow:us': 'Theo dõi'
  'Download the app': 'Tải ứng dụng'
en:
  'brand:description': 'We help you maintain a good health and when you have problems we connect you with the best specialists.'
  'company:Info': 'Company Info'
  'Follow:us': 'Follow Us'
  'Download the app': 'Download the app'
</i18n>
