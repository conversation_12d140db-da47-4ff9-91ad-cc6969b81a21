<script setup lang="ts">
import type { NotionPage } from '~/models'

const { t } = useI18n()
interface Props {
  faqs: Hit<NotionPage>[]
  loading: boolean
}
defineProps<Props>()
</script>

<template>
  <div v-if="faqs?.length > 0">
    <template v-if="loading">
      <Skeleton
        width="100%"
        height="300px"
      />
    </template>
    <template v-else>
      <h2
        class="text-2xl font-semibold text-gray-900 lg:text-3xl dark:text-white"
      >
        {{ t('faq') }}
      </h2>
      <AccordionQuestion :items="faqs" />
    </template>
  </div>
</template>

<i18n lang="yaml">
en:
  faq: 'Frequently Asked Question'
vi:
  faq: 'Câu hỏi thường gặp'
</i18n>
