<script setup lang="ts">
const { t } = useI18n({
  useScope: 'local',
})

const actions: any[] = [
  { icon: 'pi pi-copy', label: t('copy') },
  {
    label: t('verify'),
    icon: 'pi pi-verified',
  },
]
interface Source {
  title: string
  website: string
  url: string
  icon: string
}

const config = useRuntimeConfig()
const sandboxHealthGptUrl = 'https://healthgpt-wellcare-vn.mhealthvn.com/'
const productionHealthGptUrl = 'https://healthgpt.wellcare.vn/'
const healthGptUrl = computed(() =>
  config.public.appEnv === 'sandbox'
    ? sandboxHealthGptUrl
    : productionHealthGptUrl,
)

const sources: Source[] = [
  {
    title: 'Nutrition for People with Cancer.',
    website: 'cancer.org',
    icon: 'https://www.cancer.org/content/dam/cancer-org/images/logos/acs/acs-logo.svg',
    url: 'https://www.cancer.org/cancer/survivorship/coping/nutrition.html',
  },
]
</script>

<template>
  <div
    class="mx-4 my-20 flex flex-col items-stretch justify-between gap-8 md:mx-8 md:flex-row lg:gap-16 xl:mx-20 2xl:mx-60"
  >
    <div class="min-w-80 max-w-96 space-y-5">
      <div class="flex flex-row items-center gap-1">
        <NuxtImg
          src="images/homepage/logoGPT.png"
          alt="HealthGPT"
          class="size-14"
        />
        <h4 class="m-0">
          HealthGPT
        </h4>
      </div>
      <h2>{{ t("title") }}</h2>
      <p>
        {{ t("description") }}
      </p>
      <NuxtLink
        :to="healthGptUrl"
        target="_blank"
        rel="noopener"
        class="flex items-center"
      >
        <Button :label="t('tryNow')" />
      </NuxtLink>
    </div>
    <div class="flex max-w-2xl flex-grow flex-col justify-center gap-5 sm:min-w-96">
      <PageHomeHealthGPTMessageBubble
        class="self-start"
        avatar="/images/homepage/UserAskAIAvatar.jpg"
        :message="t('message1')"
        side="right"
      />

      <PageHomeHealthGPTMessageBubble
        class="self-end"
        side="left"
        avatar="images/homepage/logoGPT.png"
        :message="t('message2')"
        :actions="actions"
      >
        <template #footer>
          <div>
            <p class="font-medium">
              {{ t("source") }}
            </p>
            <div class="flex flex-row flex-wrap gap-2">
              <NuxtLink
                v-for="source in sources"
                :key="source.title"
                class="flex items-center px-2 py-1 border dark:border-gray-600 transition border-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg gap-2"
                :to="source.url"
                target="_blank"
                rel="noopener"
              >
                <NuxtImg
                  class="size-5 rounded-full"
                  :src="source.icon"
                />
                <div class="leading-tight">
                  <small class="text-gray-600 dark:text-gray-400">{{
                    source.website
                  }}</small>
                  <p>{{ source.title }}</p>
                </div>
              </NuxtLink>
            </div>
          </div>
        </template>
      </PageHomeHealthGPTMessageBubble>
    </div>
  </div>
</template>

<i18n lang="yaml">
en:
  title: "Your science-backed health companion"
  description: "A comprehensive AI Health GPT that delivers reliable, evidence-based health information, with the option for verification by healthcare professionals."
  tryNow: "Try now"
  message1: "I am undergoing chemotherapy for ovarian cancer. Should I drink a lot of orange juice to boost my immune system?"
  message2: "Drinking orange juice in moderation is safe and provides hydration and essential nutrients, such as antioxidants and flavonoids, which may help support overall health during chemotherapy. However, orange juice does not directly boost the immune system or affect cancer treatment outcomes. Avoid excessive consumption, as it may lead to stomach irritation or interfere with certain medications. Always consult your oncologist or dietitian before making dietary changes during chemotherapy."
  source: "Citation"
  copy: "Copy"
  verify: "Verify with expert"
vi:
  title: "Giải đáp sức khỏe dựa trên bằng chứng khoa học"
  description: "Một AI Health GPT toàn diện, cung cấp thông tin sức khỏe đáng tin cậy dựa trên Y Học Chứng Cứ, với tùy chọn nhận định thêm từ các chuyên gia."
  tryNow: "Thử ngay"
  message1: "Tôi đang điều trị ung thư buồng trứng bằng hóa trị, có nên uống nhiều nước cam để tăng sức đề kháng?"
  message2: "Uống nước cam ở mức độ vừa phải là an toàn và cung cấp nước cùng các chất dinh dưỡng cần thiết, như chất chống oxy hóa và flavonoid, giúp hỗ trợ sức khỏe tổng thể trong quá trình hóa trị. Tuy nhiên, nước cam không trực tiếp tăng cường hệ miễn dịch hoặc ảnh hưởng đến kết quả điều trị ung thư. Tránh uống quá nhiều vì có thể gây kích ứng dạ dày hoặc ảnh hưởng đến một số loại thuốc. Hãy tham khảo ý kiến bác sĩ hoặc chuyên gia dinh dưỡng trước khi thay đổi chế độ ăn."
  source: "Nguồn tham khảo"
  copy: "Sao chép"
  verify: "Chuyên gia nhận định"
</i18n>
