import { ElasticIndex } from '~/models'
import type { RequestElasticSearch } from '~/models'

function createConfig(): Record<ElasticIndex, RequestElasticSearch> {
  // const { locale } = useI18n()
  const config: any = useRuntimeConfig()
  const site = config.public.page.site
  const status = config.public.page.status

  return {
    [ElasticIndex.NOTION_WEBSITE]: {
      query: {
        filter: [
          { terms: { 'page.properties.Site.keyword': site.split(',') } },
          {
            terms: {
              'page.properties.Status.keyword': status.split(','),
            },
          },
          // {
          //   term: { 'page.properties.Locale.keyword': locale.value },
          // },
        ],
        should: [],
      },
      sort: [{ 'page.properties.Order': 'desc' }, '_score'],
      _source: {
        excludes: [
          'page.properties.Sub-item',
          'page.properties.Labels',
          'page.properties.Status',
          'page.properties.Site',
          'page.archived',
          'page.created_time',
        ],
        includes: ['page'],
      },
    },
    [ElasticIndex.CATALOG_PRODUCT]: {
      query: {
        filter: [],
        should: [],
        must: [],
      },
      sort: [{ 'output.provider.order': 'desc' }],
      _source: {
        includes: ['output.provider'],
      },
    },
    [ElasticIndex.NOTION_RATINGS]: {
      query: {
        filter: [],
        should: [],
        must: [],
      },
      sort: ['_score'],
      _source: { includes: ['page'] },
    },
    [ElasticIndex.NOTION_BANNERS]: {
      query: {
        filter: [
          {
            term: {
              'page.properties.Platforms.keyword': 'web',
            },
          },
          {
            term: {
              'page.properties.Status.keyword': 'Done',
            },
          },
          {
            range: {
              'page.properties.Valid.start': {
                lt: 'now',
              },
            },
          },
          {
            range: {
              'page.properties.Valid.end': {
                gt: 'now',
              },
            },
          },
        ],
        should: [],
        must: [],
      },
      sort: ['_score'],
      _source: { includes: ['page.properties'] },
    },
    [ElasticIndex.NOTION_ASK_DOCTOR]: {
      query: {
        filter: [
          {
            terms: {
              'page.properties.Status.keyword': status.split(','),
            },
          },
        ],
        should: [],
        must: [],
      },
      sort: ['_score'],
      _source: {
        includes: ['page'],
      },
    },
    [ElasticIndex.RATINGS_HOMEPAGE]: {
      query: {
        filter: [],
        should: [],
        must: [],
      },
      sort: ['_score'],
      _source: { includes: ['page'] },
    },
    [ElasticIndex.PROVIDERS]: {},
    [ElasticIndex.NOTION_SPECIALTIES]: {},
    [ElasticIndex.BROADCAST_HEALTH_PROGRAM]: {},
    [ElasticIndex.NOTION_CARETEAM_KNOWLEDGE]: {},
    [ElasticIndex.NOTION_CONDITIONS]: {},
    [ElasticIndex.NOTION_CONTENT_HEALTH_PROGRAM]: {},
    [ElasticIndex.NOTION_EDU_HUB]: {},
  }
}

export function useFetchDefault(index: ElasticIndex) {
  const config = createConfig()
  const filter = config[index].query.filter
  const sort = config[index].sort
  const should = config[index].query.should
  const _source = config[index]._source

  return { filter, sort, should, _source }
}
