<script setup lang="ts">
import type { ComponentBanner } from '~/models'

const { progress } = useScrollProgress()
const visible = ref(false)
const shown = ref(0)
interface Props {
  banner: ComponentBanner
}
const props = defineProps<Props>()
const emit = defineEmits(['click', 'impress'])
watch(progress, () => {
  if (progress.value > 95 && shown.value < 1) {
    setTimeout(() => {
      visible.value = true
      emit('impress', props.banner)
      shown.value++
    }, 1000)
  }
})
const handleClick = (data: any) => {
  visible.value = false
  emit('click', data)
}
</script>

<template>
  <div v-if="banner">
    <Dialog
      v-model:visible="visible"
      dismissable-mask
      close-on-escape
      :show-header="true"
      modal
      closable
      :pt="{
        root: 'max-w-screen-xl mx-auto relative',
        icons: 'absolute top-0 right-0',
        content: 'px-0',
        header: 'p-5 bg-transparent',
      }"
    >
      <NuxtImg
        :src="banner.source"
        @click="handleClick"
      />
    </Dialog>
  </div>
</template>
