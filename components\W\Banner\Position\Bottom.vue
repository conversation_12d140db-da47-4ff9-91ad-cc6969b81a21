<script setup lang="ts">
import type { ComponentBanner } from '~/models'

const { banner } = defineProps({
  banner: {
    type: Object as PropType<ComponentBanner>,
    default: () => ({}),
  },
})

defineEmits(['click'])
</script>

<template>
  <!-- <div
    class="fixed bottom-0 left-0 w-full z-50 p-4 bg-blue-500 text-white"
    @click="$emit('click')"
  >
    <p>This is a bottom banner. {{ banner }}</p>
  </div> -->
  <NuxtImg
    v-if="banner"
    :src="banner.source"
    class="w-full cursor-pointer rounded-md"
    @click="$emit('click')"
  />
</template>
