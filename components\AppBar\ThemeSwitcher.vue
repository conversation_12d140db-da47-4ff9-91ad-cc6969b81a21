<script setup lang="ts">
// const isDark = useDark()
// const toggleTheme = useToggle(isDark)
const color = useColorMode()

function toggleTheme() {
  color.preference = color.value === 'dark' ? 'light' : 'dark'
}
</script>

<template>
  <div>
    <label for="theme">switch theme</label>

    <Button
      id="theme"
      text
      :icon="`pi ${color.value === 'dark' ? 'pi-sun' : 'pi-moon'}`"
      size="small"
      outlined
      rounded
      aria-label="switch theme"
      @click="toggleTheme()"
    />
  </div>
</template>
