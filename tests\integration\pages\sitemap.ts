import { it, expect } from 'vitest'
import { $fetch } from '@nuxt/test-utils/e2e'

export const sitemap = async () => {
  it('should generate index of 2 sitemaps', async () => {
    const sitemap = await $fetch('/sitemap.xml')
    expect(sitemap).toContain('/vi-VN-sitemap.xml</loc>')
    expect(sitemap).toContain('/en-US-sitemap.xml</loc>')
  })
  it('should contain vi-VN-sitemap.xml', async () => {
    const sitemap = await $fetch('/vi-VN-sitemap.xml')
    expect(sitemap).toContain('<urlset')
  })
  it('should contain en-US-sitemap.xml', async () => {
    const sitemap = await $fetch('/en-US-sitemap.xml')
    expect(sitemap).toContain('<urlset')
  })
}

export default sitemap
