<script setup lang="ts">
import type { PropType } from 'vue'
import type { Hit, NotionPage } from '~/models'

type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
defineProps({
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['title', 'sapo'],
  },
  article: {
    type: Object as PropType<Hit<NotionPage>>,
    required: true,
  },
  slug: {
    type: String,
    default: '',
  },
})
// const placeholderSource = computed<string>(() => {
//   const name = (props.article as any).page?.properties?.Name
//   return (
//     'https://placehold.co/1920x1080?text='
//     + name.split(' ').slice(0, 3).join(' ')
//     + ' ...'
//   )
// })
</script>

<template>
  <article class="flex flex-col gap-4 md:gap-5">
    <slot
      v-if="blocks.includes('thumbnail')"
      name="thumbnail"
    >
      <template v-if="(article as Hit<NotionPage>).page?.cover?.url">
        <div class="overflow-hidden rounded-xl hover:cursor-pointer">
          <NuxtImg
            :src="(article as Hit<NotionPage>).page?.cover?.url"
            :alt="(article as Hit<NotionPage>).page?.properties?.Name"
            loading="lazy"
            preset="thumbnail"
            width="320"
            height="180"
            :modifiers="{ fit: 'cover' }"
            format="webp"
            sizes="sm:160px md:200px lg:320px"
            class="pultransform transition duration-800 ease-in hover:scale-105 w-full"
            :style="{
              backgroundImage:
                'linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%)',
              backgroundSize: '200% 100%',
            }"
          />
        </div>
      </template>
    </slot>

    <slot
      v-if="blocks.includes('title')"
      name="title"
    >
      <!-- <NuxtLink :to="`${slug}/${article.page.properties.Slug}`"> -->
      <div
        class="hover:text-primary mb-2 text-lg font-bold tracking-tight text-gray-900 md:text-xl dark:text-white"
      >
        {{ article?.page?.properties?.Name }}
      </div>
      <!-- </NuxtLink> -->
    </slot>

    <slot
      v-if="blocks.includes('sapo')"
      name="sapo"
    >
      <p class="mb-3 font-normal text-gray-700 dark:text-gray-400">
        {{ article?.page?.properties?.Sapo }}
      </p>
    </slot>
  </article>
</template>
