<!-- pages/cam-nang.vue -->
<script setup lang="ts">
import { ElasticIndex } from '~/models'
import { navigateTo } from '#imports'

const { t } = useI18n()
const switchLocalePath = useSwitchLocalePath()

const {
  hit: category,
  status,
  loading: categoryLoading,
} = useFetchElasticWithDefault(ElasticIndex.NOTION_WEBSITE, {
  size: 1,
  filters: [{ term: { 'page.properties.Slug.keyword': 'cam-nang' } }],
})

watch(
  [categoryLoading, category],
  async () => {
    if (!categoryLoading.value && !category.value) {
      await navigateTo(switchLocalePath('vi'))
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

usePageArticle({
  hit: category,
  status: status,
  jsonld: { enabled: false },
})
const { data: parents, loading }: any = useFetchPageSubItem({
  page: {
    filters: [
      {
        term: {
          'page.properties.Parent item.properties.Slug.keyword': 'cam-nang',
        },
      },
    ],
    size: 10,
  },
  subitem: {
    _source: { includes: ['page.properties.Name', 'page.properties.Slug'] },
    size: 100,
  },
})

const tabs = computed(() =>
  parents.value?.hits.map((parent: any) => parent.page.properties.Name),
)

const articles = computed(() => {
  const output = {}

  parents.value?.hits.forEach((parent: any) => {
    output[parent?.page?.properties.Name] = {}

    parent.inner_hits?.['most_recent']?.hits.hits.forEach((inner_hit: any) => {
      const initial = inner_hit.page.properties.Name[0].toUpperCase()

      if (!output[parent.page.properties.Name][initial])
        output[parent.page.properties.Name][initial] = []

      output[parent.page.properties.Name][initial].push({
        label: inner_hit.page.properties.Name,
        route: inner_hit.page.properties.Slug,
      })
    })
  })

  return output
})

const selectedTabIndex = ref(0)

const selectedTabLabel = computed(
  () => Object.keys(articles.value)[selectedTabIndex.value],
)
const initials = computed(() => {
  return Object.keys(articles.value[selectedTabLabel.value]).sort(
    (a: string, b: string) => a.localeCompare(b),
  )
})
</script>

<template>
  <div>
    <div
      v-if="loading || !category"
      class="mx-auto mb-10 max-w-7xl"
    >
      <WSkeleton block="article" />
    </div>
    <div v-else>
      <WHeroSection
        class="h-96"
        :blocks="['overlay', 'title', 'sub-title']"
        :title="category?.page?.properties?.Name"
        :sub-title="category?.page?.properties?.Sapo"
        seo-title-tag="h1"
      />

      <div
        class="flex flex-col px-8 pb-10 pt-3 md:px-24 lg:px-32 dark:bg-slate-800"
      >
        <TabView v-model:active-index="selectedTabIndex">
          <TabPanel
            v-for="(tab, indexTabs) in tabs"
            :key="indexTabs"
            :header="tab"
          >
            <h3 class="h3 text-lg font-semibold">
              {{ t('choose-a-letter') }}
            </h3>

            <div class="flex flex-wrap justify-start gap-2">
              <NuxtLink
                v-for="(char, indexChar) in initials"
                :key="indexChar"
                :to="`#${tab}${char}`"
              >
                <Button
                  size="large"
                  :label="char"
                  severity="secondary"
                />
              </NuxtLink>
            </div>
            <div
              v-for="(initial, indexInit) in initials"
              :key="indexInit"
              class="flex flex-col self-start"
            >
              <h2
                :id="tab + initial"
                class="h2 bg-surface-100 mr-auto rounded-xl px-3 py-2 text-5xl font-bold dark:bg-slate-700"
              >
                {{ initial }}
              </h2>
              <div
                class="grid grid-cols-1 justify-items-start gap-2 sm:grid-cols-2 md:grid-cols-3"
              >
                <NuxtLink
                  v-for="(article, indexArticle) in articles[selectedTabLabel][
                    initial
                  ]"
                  :key="indexArticle"
                  :to="`/${useSlug(tab as string)}/${article?.route}`"
                >
                  <Button
                    link
                    size="large"
                    class="text-start"
                  >
                    {{ article?.label }}
                  </Button>
                </NuxtLink>
              </div>
            </div>
          </TabPanel>
        </TabView>
      </div>
    </div>
  </div>
</template>

<style>
.h2 {
  margin-top: 70px;
  margin-bottom: 30px;
}
.h3 {
  margin-top: 16px;
  margin-bottom: 8px;
}
</style>

<i18n lang="yaml">
en:
  'choose-a-letter': 'Choose a letter'
vi:
  'choose-a-letter': 'Chọn ký tự'
</i18n>
