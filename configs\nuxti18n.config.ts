import type { NuxtI18nOptions } from '@nuxtjs/i18n'

export const i18n = {
  baseUrl: 'https://wellcare.vn',
  // lazy: false,
  locales: [
    {
      code: 'en',
      iso: 'en-US',
      name: 'English',
    },
    {
      code: 'vi',
      iso: 'vi-VN',
      name: 'Tiếng Việt',
    },
  ],
  strategy: 'prefix_except_default',
  detectBrowserLanguage: {
    useCookie: true,
    cookieKey: 'i18n_locale',
    redirectOn: 'root',
  },
  defaultLocale: 'vi',
  vueI18n: './configs/vuei18n.config.ts',
} as NuxtI18nOptions
