<!-- components/page/category -->
<script setup lang="ts">
import { ElasticIndex } from '~/models'
import type { NotionPage, NotionWebsitePageProperties } from '~/models'

type LayoutType = 'list' | 'grid' | 'timeline' | 'gallery'
type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'

const props = defineProps({
  page: {
    type: Object as PropType<NotionPage<NotionWebsitePageProperties>>,
    required: true,
  },
  containerList: {
    type: String as PropType<'timeline' | 'iterator'>,
    default: 'iterator',
  },
  layout: {
    type: String as PropType<LayoutType>,
    default: () => 'list',
  },
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['title', 'sapo'],
  },
  fetch: {
    type: Object,
    default: () => ({}),
  },
})
const category: ComputedRef<NotionPage<NotionWebsitePageProperties>> = computed(
  () => props.page,
)
const title = computed(() => category.value.properties.Name)
const pageSize = ref(20)
const route = useRoute()
const from = ref(parseInt((route.query.page as any) || 0))
const { data, loading } = useFetchElasticWithDefault(
  ElasticIndex.NOTION_WEBSITE,
  {
    from: from,
    size: pageSize,
    filters: [
      {
        term: {
          'page.properties.Parent item.properties.Slug.keyword':
            category.value.properties.Slug,
        },
      },
    ],
    ...props.fetch,
  },
)
</script>

<template>
  <div :key="category?.id">
    <WHeroSection
      class="h-96"
      :blocks="['background-image', 'overlay', 'title', 'sub-title', 'under']"
      :background-image="{
        src: category?.cover?.url,
        alt: category?.properties.Name,
      }"
      :title="title"
      :sub-title="category?.properties.Sapo"
      seo-title-tag="h1"
      small-header
    >
      <template #under>
        <slot name="actions"></slot>
      </template>
    </WHeroSection>
    <slot
      :articles="data?.hits"
      :loading="loading"
    >
      <div class="mt-[30px] py-4">
        <div class="mx-auto flex max-w-screen-md flex-col items-stretch gap-8">
          <PageCategoryArticleList
            :articles="data?.hits"
            :slug="category.properties.Slug"
            :container="props.containerList"
            :layout="props.layout"
            :blocks="props.blocks"
            :loading="loading"
          />
        </div>
      </div>
    </slot>
    <PageCategoryPaginator
      :total-records="data?.total"
      :rows="pageSize"
      @update:first="from = $event"
    />
    <!-- <WJsonViewer header="data" :data="data" /> -->
  </div>
</template>
