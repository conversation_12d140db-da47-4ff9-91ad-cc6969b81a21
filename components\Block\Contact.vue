<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

type BlockType = 'email' | 'call' | 'faq'

const { blocks } = defineProps({
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['email', 'call', 'faq'],
  },
})

const { t } = useI18n()
const router = useRouter()

const onClick = (block: BlockType) => {
  switch (block) {
    case 'call':
      window.location.href = 'tel:+842836226822'
      break
    case 'email':
      window.location.href = 'mailto:<EMAIL>'
      break
    case 'faq':
      router.push('/cau-hoi-thuong-gap')
      break
  }
}
</script>

<template>
  <div class="container">
    <div class="mt-16 flex items-center justify-around gap-8 md:gap-16">
      <div
        v-for="block in blocks"
        :key="block"
        class="bg-surface-50 dark:bg-surface-900 hover:bg-surface-100 hover:border-surface-100 dark:hover:border-surface-500 group flex cursor-pointer flex-col items-start gap-4 rounded-lg border border-transparent p-5 transition-all md:-m-5"
        @click="onClick(block)"
      >
        <div
          class="bg-primary/10 shadow-primary/50 border-primary mt-1 grid h-10 w-10 shrink-0 place-items-center rounded-lg border p-2 shadow transition-colors"
        >
          <svg
            v-if="block === 'email'"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            class="text-primary"
          >
            <path
              fill="currentColor"
              d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"
            />
          </svg>
          <svg
            v-else-if="block === 'call'"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            class="text-primary"
          >
            <path
              fill="currentColor"
              d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56a.977.977 0 0 0-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02c-.37-1.11-.56-2.3-.56-3.53c0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99C3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z"
            />
          </svg>
          <svg
            v-else
            width="24"
            height="24"
            viewBox="0 0 24 24"
            class="text-primary"
          >
            <path
              fill="currentColor"
              d="M21 5c-1.11-.35-2.33-.5-3.5-.5c-1.95 0-4.05.4-5.5 1.5c-1.45-1.1-3.55-1.5-5.5-1.5S2.45 4.9 1 6v14.65c0 .25.25.5.5.5c.1 0 .15-.05.25-.05C3.1 20.45 5.05 20 6.5 20c1.95 0 4.05.4 5.5 1.5c1.35-.85 3.8-1.5 5.5-1.5c1.65 0 3.35.3 4.75 1.05c.**********.25.05c.25 0 .5-.25.5-.5V6c-.6-.45-1.25-.75-2-1zm0 13.5c-1.1-.35-2.3-.5-3.5-.5c-1.7 0-4.15.65-5.5 1.5V8c1.35-.85 3.8-1.5 5.5-1.5c1.2 0 2.4.15 3.5.5v11.5z"
            />
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-semibold">
            {{ t(`${block}:label`) }}
          </h3>
          <p class="mt-2 leading-relaxed text-slate-500">
            {{ t(`${block}:description`) }}
          </p>
        </div>
        <button
          class="text-primary hover:text-primary transition-colors"
          @click="onClick(block)"
        >
          {{ block === 'faq' ? t('faq:link') : t(`${block}:action`) }}
        </button>
      </div>
    </div>
  </div>
</template>

<i18n lang="json">
{
  "en": {
    "email:label": "Email us",
    "email:description": "We're here to help with any questions or concerns.",
    "email:action": "Send an email",
    "call:label": "Call us",
    "call:description": "Speak directly with our support team.",
    "call:action": "Start a call",
    "faq:label": "Visit our FAQ",
    "faq:description": "Find quick answers to common questions.",
    "faq:link": "Browse FAQ"
  },
  "vi": {
    "email:label": "Gửi Email",
    "email:description": "Chúng tôi sẵn sàng giải đáp mọi thắc mắc của bạn.",
    "email:action": "Gửi email ngay",
    "call:label": "Gọi điện",
    "call:description": "Trao đổi trực tiếp với đội ngũ hỗ trợ của chúng tôi.",
    "call:action": "Gọi ngay",
    "faq:label": "Xem câu hỏi thường gặp",
    "faq:description": "Tìm câu trả lời nhanh cho những câu hỏi phổ biến.",
    "faq:link": "Xem FAQ"
  }
}
</i18n>
