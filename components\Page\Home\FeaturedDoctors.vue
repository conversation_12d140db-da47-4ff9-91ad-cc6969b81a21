<script setup lang="ts">
const doctors = [
  {
    title: '<PERSON><PERSON><PERSON><PERSON>',
    image: {
      src: 'https://storage.googleapis.com/cms-gallery/61e64aefe0ab54e82fc38eef/e0b8dfd9-a409-434a-beef-edaa7accb800.jpg',
      alt: '<PERSON><PERSON><PERSON><PERSON>',
    },
    description:
      'Bs Tr<PERSON> Đoàn là một bác sĩ Nhi nổi tiếng với phong cách tư vấn bệnh không lạm dụng thuốc và xét nghiệm, dựa trên y học chứng cứ. Cách tiếp cận vấn đề đặc biệt của bác sĩ được tóm lược trong cuốn “Để con được ốm” và đã truyền cảm hứng cho hàng triệu phụ huynh và hàng ngàn bác sĩ khác thực hành theo.',
  },
  {
    title: 'Tr<PERSON><PERSON><PERSON>',
    image: {
      src: 'https://storage.googleapis.com/wellcare-user-profile/614a8075af355fbb79033ed5/4o57dnx9szgc5afo.jpg',
      alt: 'Trương Hữu Khanh',
    },
    description:
      'Bs Khanh là một người có tầm ảnh hưởng lớn trong ngành y, đặc biệt về bệnh truyền nhiễm. Được biết tới với một phong cách mộc mạc và nhân hậu, bác sĩ dành nhiều thời gian của mình để đào tạo sức khỏe cộng đồng thông qua trang “Hỏi bác sĩ Nhi đồng” với gần 300 ngàn người theo dõi.',
  },
  {
    title: 'Phan Quốc Bảo',
    image: {
      src: 'https://storage.googleapis.com/wellcare-user-profile/614a8075af355ff8a2033ed4/68d26a97-b09e-514c-a9c0-5370dd5adbee.jpeg',
      alt: 'Phan Quốc Bảo',
    },
    description:
      'Bác sĩ công tác tại Bệnh viện Đại học Y Dược Tp.HCM, có kinh nghiệm trên 20 năm trong tư vấn và điều trị các bệnh lý Tai Mũi Họng.',
  },
  {
    title: 'Trần Thị Hồng An',
    image: {
      src: 'https://storage.googleapis.com/cms-gallery/61e64af1e0ab549317c38ef5/453b9a52-94d6-46d9-86ca-9fd2777f6ba9.png',
      alt: 'Trần Thị Hồng An',
    },
    description:
      'Thạc sĩ Y khoa chuyên ngành Nội tổng quát, có gần 30 năm kinh nghiệm trong nghề. Hoàn thành khóa học tại Sanford Medical Center bang South Dakota Hoa Kỳ về: Nội Khoa, Hô hấp, Khớp, Cấp cứu.',
  },
]
</script>

<template>
  <WMemberSection
    class="h-[1640px] sm:h-[980px] md:h-[900px]"
    :blocks="['title', 'sub-title', 'under']"
    title="Bác sĩ nổi bật"
    sub-title="Niềm tự hào của Wellcare"
    :items="doctors"
    small-header
    wrap-item
  >
    <template #default="{ member }">
      <div
        :class="`mx-4 mt-6 flex min-h-[300px] flex-col items-center first:mt-0 sm:mt-0 sm:min-h-[400px] sm:max-w-[42%] md:min-h-[380px] md:max-w-[44%] lg:min-h-[344px]`"
      >
        <WImage
          :class="`overflow-hidden rounded-full border border-stone-200 hover:cursor-pointer hover:border-teal-500 dark:border-stone-500 dark:hover:border-teal-500`"
          :src="member.image?.src"
          :alt="member.title"
          width="200"
          height="200"
          image-class="transition ease-linear hover:scale-105"
        />
        <h2 class="text-dark py-2 text-xl font-semibold dark:text-white">
          {{ member.title }}
        </h2>
        <p class="text-sm text-stone-500 dark:text-stone-400">
          {{ member.description }}
        </p>
      </div>
    </template>
  </WMemberSection>
</template>
