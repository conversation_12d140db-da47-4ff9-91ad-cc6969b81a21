import {
  sitemap,
  googleFonts,
  image,
  app,
  robots,
  primevue,
  i18n,
  runtimeConfig,
} from './configs'

const env = process.env

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  modules: [
    '@nuxt/image',
    '@nuxt/test-utils/module',
    '@nuxtjs/google-fonts',
    '@nuxtjs/i18n',
    '@nuxtjs/robots',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    '@wellcare/muot-ui',
    '@nuxtjs/tailwindcss',
    '@wellcare/nuxt3-module-data-layer',
    '@wellcare/nuxt3-module-form',
    '@zadigetvoltaire/nuxt-gtm',
    '@nuxt/eslint',
    'dayjs-nuxt',
    'nuxt-jsonld',
    'nuxt-marquee',
    'nuxt-primevue',
    // If you use other modules (eg. nuxt-i18n), always declare the sitemap module at end of array
    '@nuxtjs/sitemap',
  ],

  ssr: true,
  devtools: { enabled: env.NUXT_DEVTOOLS_ENABLED === 'true' },
  app,

  css: ['~/assets/styles/index.css', 'primeicons/primeicons.css'],

  site: {
    url: env.SITE_URL,
  },
  runtimeConfig: runtimeConfig(env),

  build: {
    transpile: ['vue-echarts', 'resize-detector'],
  },

  devServer: {
    host: env.APP_HOST as any,
    port: env.APP_PORT as any,
  },

  experimental: { typedPages: true, appManifest: false },
  compatibilityDate: '2024-08-30',
  typescript: { shim: false, strict: true },

  dayjs: {
    locales: ['vi', 'en'],
    plugins: ['utc', 'localizedFormat'],
    defaultLocale: 'vi',
  },

  eslint: {
    config: {
      stylistic: true,
    },
  },
  googleFonts,
  i18n,
  image,
  primevue,
  robots,
  sitemap,
  tailwindcss: {
    cssPath: ['./assets/styles/global.css', { injectPosition: 'first' }],
    configPath: 'tailwind.config',
    exposeConfig: {
      level: 2,
    },
    config: {
    },
    viewer: true,
  },
})
