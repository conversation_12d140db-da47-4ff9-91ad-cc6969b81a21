export const useLayoutStore = defineStore('layout', () => {
  const isProgress = ref<boolean>(false)

  const getIsProgress = computed<boolean>(() => isProgress.value)

  const setIsProgress = (val: boolean) => {
    isProgress.value = val
  }

  return { isProgress, setIsProgress, getIsProgress }
})

if (import.meta.hot)
  import.meta.hot.accept(
    acceptHMRUpdate(useLayoutStore as any, import.meta.hot),
  )
