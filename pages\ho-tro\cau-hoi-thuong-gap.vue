<script setup lang="ts">
import { definePageMeta, useI18n } from '#imports'

definePageMeta({
  middleware: 'redirect-to-vi',
  colorMode: 'light',
})

const { t } = useI18n()
const { path } = useRoute()
const { title, sapo, faqGroups, loading }: any = usePageFAQ()
</script>

<template>
  <div>
    <div
      v-if="loading"
      class="mx-auto max-w-7xl"
    >
      <WSkeleton block="article" />
    </div>

    <SideMenu
      v-else
      :useful-link-label="t('faq-list')"
    >
      <template #side>
        <NuxtLinkLocale
          v-for="faqGroup in faqGroups"
          :key="faqGroup.name"
          class="hover:text-primary my-1 cursor-pointer text-wrap"
          :to="`${path}#${faqGroup.slug}`"
        >
          {{ faqGroup.name }}
        </NuxtLinkLocale>
      </template>

      <template #main>
        <h1 v-if="title">
          {{ title }}
        </h1>

        <h5
          v-if="sapo"
          class="mb-16 max-w-[40rem]"
        >
          {{ sapo }}
        </h5>

        <div
          v-for="faqGroup in faqGroups"
          :key="faqGroup.name"
          class="my-4"
        >
          <h4 :id="faqGroup.slug">
            {{ faqGroup.name }}
          </h4>
          <Accordion>
            <AccordionTab
              v-for="(faq, index) in faqGroup.faqs"
              :key="index"
              :header="faq.question"
            >
              <WArticleBody
                :blocks="faq?.answer"
                class="px-4"
              />
            </AccordionTab>
          </Accordion>
        </div>

        <BlockContact :blocks="['email', 'call']" />
      </template>
    </SideMenu>
  </div>
</template>

<i18n lang="yaml">
en:
  faq-list: 'Faq list'
vi:
  faq-list: 'Danh sách câu hỏi thường gặp'
</i18n>
