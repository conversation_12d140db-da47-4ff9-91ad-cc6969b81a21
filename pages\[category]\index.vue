<!-- pages/[category]/index.vue -->
<script setup lang="ts">
import { ElasticIndex } from '~/models'

definePageMeta({
  colorMode: 'light',
})

const route = useRoute()
const { t } = useI18n()

const slug = computed(() => (route.params as any).category)

const { hit, status, loading } = useFetchElasticWithDefault(
  ElasticIndex.NOTION_WEBSITE,
  {
    size: 1,
    filters: [{ term: { 'page.properties.Slug.keyword': slug.value } }],
    _source: {
      includes: ['page', 'blockstring'],
    },
  },
)

watch(
  () => [loading.value, hit.value],
  () => {
    if (!loading.value && !hit.value) {
      showError({
        statusCode: 404,
        statusMessage: t('error:page-not-found:message'),
        cause: t('error:page-not-found:cause'),
        fatal: true,
      })
    }
  },
  { immediate: true },
)

usePageArticle({
  hit,
  status,
})

const breadcrumbs = computed(() => {
  const output = [{ label: t('breadcrumb:home'), route: '/' }]
  if (hit.value.page.properties['Parent item'].length > 0) {
    output.push({
      label: hit.value.page.properties['Parent item'][0].properties.Name,
      route: hit.value.page.properties['Parent item'][0].properties.Slug,
    })
  }
  return output
})

// SEO meta tags setup using `useSeoMeta` hook
useSeoMeta({
  title: computed(() => `${hit.value?.page?.properties?.Name} - Wellcare`),
  ogTitle: computed(() => `${hit.value?.page?.properties?.Name} - Wellcare`),
  description: computed(
    () => `${hit.value?.page?.properties['Meta Description']}` || '',
  ),
  ogDescription: computed(
    () => `${hit.value?.page?.properties['Meta Description']}` || '',
  ),
  ogImage: computed(() => `${hit.value?.page?.cover?.url}` || ''),
})
</script>

<template>
  <div>
    <div
      v-if="loading || !hit"
      class="mx-auto max-w-7xl p-6"
    >
      <WSkeleton block="article" />
    </div>
    <PageCategory
      v-else-if="hit && hit.page.properties.Type === 'Category'"
      :page="hit.page"
    />
    <PageArticle
      v-else-if="hit"
      :breadcrumbs="breadcrumbs"
      :page="hit.page"
    />
  </div>
</template>
