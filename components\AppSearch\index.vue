<script setup lang="ts">
const keyword = ref<string>('')

const route = useRoute()
const router = useRouter()

// function onCloseSearch() {
//   if (keyword.value.trim() === '') router.push({ query: {} })
//   else keyword.value = ''
// }

function onSearch() {
  if (keyword.value.trim() === '') return
  const target = route.query?.target ?? 'tim-kiem'
  if (target === route.fullPath) {
    router.push({
      query: {
        q: keyword.value,
      },
    })
  }
  else {
    router.push({ query: {} })
    navigateTo({
      path: target,
      query: {
        q: keyword.value,
      },
    })
  }
  keyword.value = ''
}
</script>

<template>
  <div class="flex justify-center">
    <IconField
      icon-position="left"
      class="mr-2 flex-grow"
    >
      <InputIcon class="pi pi-search" />
      <InputText
        id="search_input_el"
        v-model="keyword"
        @keypress.enter="onSearch"
      />
    </IconField>
    <Button
      label="Search"
      class="shrink-0"
      @click="onSearch"
    />
  </div>
</template>
