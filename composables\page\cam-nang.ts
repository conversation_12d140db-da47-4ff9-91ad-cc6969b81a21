import { useFetchElasticWithDefault } from '../fetch/elastic-with-default'
import { ElasticIndex } from '~/models'

export function usePageCamNang() {
  const collapseField = 'page.properties.Parent item.id.keyword'
  const {
    hit: category,
    loading: loadingCategoryPage,
    status,
  } = useFetchElasticWithDefault(ElasticIndex.NOTION_WEBSITE, {
    filters: [{ term: { 'page.properties.Slug.keyword': 'cam-nang' } }],
  })

  usePageArticle({
    hit: category,
    status: status,
    jsonld: { enabled: false },
  })

  const { data: parents, loading: loadingParents }: any = useFetchPageSubItem({
    page: {
      filters: [
        {
          term: {
            'page.properties.Parent item.properties.Slug.keyword': 'cam-nang',
          },
        },
      ],
      size: 10,
    },
    subitem: {
      _source: { includes: ['page.properties.Name', 'page.properties.Slug'] },
      collapse: collapseField,
      size: 100,
    },
  })

  const tabs = computed(() =>
    parents.value?.hits.map((parent: any) => parent.page.properties.Name),
  )

  const articles = computed(() => {
    const output = {}

    parents.value?.hits.forEach((parent: any) => {
      output[parent.page.properties.Name] = {}

      parent.inner_hits['most_recent']?.hits.hits.forEach((inner_hit: any) => {
        const initial = inner_hit.page.properties.Name[0].toUpperCase()

        if (!output[parent.page.properties.Name][initial])
          output[parent.page.properties.Name][initial] = []

        output[parent.page.properties.Name][initial].push({
          label: inner_hit.page.properties.Name,
          route: inner_hit.page.properties.Slug,
        })
      })
    })

    return output
  })
  const loading = computed(
    () => loadingCategoryPage.value && loadingParents.value,
  )
  return {
    tabs,
    loading,
    loadingCategoryPage,
    loadingParents,
    parents,
    articles,
    category,
  }
}
