{"private": true, "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' nuxt build", "dev": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' eslint . --fix && nuxt dev -o --host", "cleanup": "npx nuxi cleanup", "start": "node .output/server/index.mjs", "generate": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' nuxt generate", "lint": "eslint . --fix", "postinstall": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' nuxt prepare", "prepare": "husky", "preview": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' nuxt preview", "readme:coverage": "istanbul-badges-readme", "test:unit": "vitest run tests/unit --coverage.enabled --coverage.reporter='json-summary'", "test:ci": "vitest run --coverage.enabled --coverage.reporter='json-summary'", "test:dev": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' vitest", "test": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' vitest run", "typecheck": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' nuxt typecheck"}, "devDependencies": {"@mdi/js": "^7.4.47", "@nuxt/devtools": "latest", "@nuxt/eslint": "^0.7.4", "@nuxt/image": "^1.7.0", "@nuxt/test-utils": "^3.13.1", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/i18n": "^8.3.1", "@nuxtjs/tailwindcss": "^6.12.0", "@playwright/test": "^1.44.1", "@vitest/coverage-istanbul": "^1.6.0", "@vitest/coverage-v8": "^1.6.0", "@vue/test-utils": "^2.4.6", "@wellcare/muot-ui": "0.2.85-primevue-v3-0.0.12", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "cross-env": "^7.0.3", "dayjs-nuxt": "^2.1.9", "eslint": "^9.17.0", "happy-dom": "^13.10.1", "husky": "^9.0.11", "istanbul-badges-readme": "^1.9.0", "nuxt": "3.13.2", "nuxt-primevue": "^0.3.1", "playwright-core": "^1.44.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "^5.7.2", "vite-plugin-vue-devtools": "^7.2.1", "vitest": "^1.6.0", "vue-tsc": "^1.8.27"}, "dependencies": {"@nuxtjs/robots": "^3.0.0", "@nuxtjs/sitemap": "^5.2.0", "@pinia/nuxt": "^0.5.1", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/nuxt": "^10.9.0", "@wellcare/nuxt3-module-data-layer": "0.4.8", "@wellcare/nuxt3-module-form": "0.8.7", "echarts": "^5.5.0", "nuxt-auth-utils": "^0.0.18", "nuxt-jsonld": "^2.0.8", "nuxt-marquee": "^1.0.4", "vue-dompurify-html": "^5.1.0", "vue-echarts": "^6.7.2"}}