<script setup lang="ts">
import type { PropType } from 'vue'
import type { Hit, NotionPage } from '~/models'

type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
type LayoutType = 'list' | 'grid' | 'timeline' | 'gallery'
const { article, blocks } = defineProps({
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['title', 'sapo'],
  },
  article: {
    type: Object as PropType<Hit<NotionPage>>,
    required: true,
  },
  slug: {
    type: String,
    required: true,
  },
  layout: {
    type: String as PropType<LayoutType>,
    default: () => 'list',
  },
})
</script>

<template>
  <PageCategoryArticleListItemLayout
    v-bind="$attrs"
    :blocks="blocks"
    :article="article"
    :slug="slug"
    :layout="layout"
  />
</template>
