import { describe, it, expect } from 'vitest'
import { compareLanguageObjects } from '../utils'
import EN from '../../lang/en.json'
import VI from '../../lang/vi.json'

describe('i18n lang files', async () => {
  const enKeys = Object.keys(EN).sort()

  it('should have all keys translated', () => {
    expect(compareLanguageObjects(EN, VI)).toBe(true)
  })

  it('should have keys as a-z or 0-9 hyphen or colon', () => {
    enKeys.forEach((key) => {
      // Exclude keys containing curly braces
      if (!/\{.*\}/.test(EN[key])) {
        expect(key).toMatch(/^[a-z0-9:-]+$/)
      }
      else {
        // If the key contains curly braces, it's acceptable
        expect(key).toMatch(/.*\{.*\}.*/)
      }
    })
  })
})
