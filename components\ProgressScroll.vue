<script setup lang="ts">
const { y } = useWindowScroll(window)

const progress = ref<number>(0)

const updateProgressIndicator = () => {
  const { documentElement, body } = document
  const windowScroll = body.scrollTop || documentElement.scrollTop
  const height = documentElement.scrollHeight - documentElement.clientHeight
  progress.value = (windowScroll / height) * 100
}

onMounted(() => {
  if (import.meta.client) {
    // init
    updateProgressIndicator()
    // scroll
    window.addEventListener('scroll', updateProgressIndicator)
  }
})
</script>

<template>
  <div>
    <ProgressBar
      v-if="y > 100"
      class="h-[3px] rounded-none"
      :value="progress"
      :show-value="false"
    />
  </div>
</template>
