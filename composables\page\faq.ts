import { ElasticIndex } from '~/models'

interface FAQ {
  slug: string
  question: string
  answer: string
}

interface FAQGroup {
  name: string
  slug: string
  faqs: FAQ[]
}

export function usePageFAQ(): any {
  const { t } = useI18n()
  const {
    hit: category,
    status: categoryStatus,
    loading: categoryLoading,
  } = useFetchElasticWithDefault(ElasticIndex.NOTION_WEBSITE, {
    size: 1,
    filters: [
      {
        term: {
          'page.properties.Slug.keyword': 'cau-hoi-thuong-gap',
        },
      },
      {
        term: {
          'page.properties.Type.keyword': 'Category',
        },
      },
    ],
  })

  const checkError = () => {
    if (
      categoryStatus.value === 'error'
      || (categoryStatus.value === 'success' && !category.value['_id'])
    ) {
      showError({
        statusCode: 404,
        statusMessage: t('error:page-not-found:message'),
        cause: t('error:page-not-found:cause'),
        fatal: true,
      })
    }
  }
  watch(categoryStatus, checkError)

  const title = computed(() => category.value?.page?.properties?.Name)

  const description = computed(() => {
    return (
      category.value?.page?.properties['Meta Description']
      || category.value?.page?.properties?.Sapo
    )
  })

  const sapo = category.value?.page?.properties?.Sapo

  useSeoMeta({
    title,
    ogTitle: category.value?.page?.properties?.Name,
    description: description.value,
    ogDescription: description.value,
    ogLocale: category.value?.page?.properties?.Locale,
  })

  const { hits: faqHits, loading: faqLoading } = useFetchElasticWithDefault(
    ElasticIndex.NOTION_WEBSITE,
    {
      size: 200,
      filters: [
        {
          term: {
            'page.properties.Type.keyword': 'FAQ',
          },
        },
      ],
      _source: {
        includes: ['page', 'blockstring'],
      },
    },
  )

  const graph = computed(() => {
    return faqHits.value?.reduce((acc: any, item: any) => {
      acc.push({
        '@type': 'Question',
        'name': item?.page?.properties?.Name,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': item?.page?.properties?.Sapo,
        },
      })
      return acc
    }, [])
  })

  useJsonld(() => ({
    '@context': 'https://schema.org',
    '@graph': graph.value,
  }))

  const faqGroups: ComputedRef<FAQGroup[]> = computed(() => {
    const faqMap = new Map<string, FAQGroup>()

    for (const hit of faqHits?.value || []) {
      const properties = hit?.page?.properties
      const parentItem = properties['Parent item']?.[0]?.properties
      const parentName = parentItem?.Name
      const parentSlug = parentItem?.Slug
      const question = properties?.Name
      const answer = hit?.page?.blocks
      const slug = properties?.Slug

      if (!parentName || !question) continue // Skip invalid entries

      const existingGroup = faqMap.get(parentName)
      if (existingGroup) {
        existingGroup.faqs.push({ slug, question, answer })
      }
      else {
        faqMap.set(parentName, {
          name: parentName,
          slug: parentSlug,
          faqs: [{ slug, question, answer }],
        })
      }
    }

    return Array.from(faqMap.values())
  })

  const loading = computed<boolean>(
    () => categoryLoading.value || faqLoading.value,
  )

  return {
    category,
    title,
    sapo,
    faqGroups,
    loading,
  }
}
