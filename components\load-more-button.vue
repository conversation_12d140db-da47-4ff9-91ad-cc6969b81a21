<template>
  <Button
    class="block min-w-28 font-medium"
  >
    <div
      v-if="props.loading"
      class="rounded-full animate-spin mx-auto w-4 h-4 rounded-inherit border-2 border-r-transparent border-surface-800 border-opacity-10"
    ></div>
    <span v-else>
      {{ t("btn:load-more") }}
    </span>
  </Button>
</template>

<script setup lang="ts">
const { t } = useI18n()
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})
</script>
