<!-- pages/song-khoe/index.vue -->
<script setup lang="ts">
import { ElasticIndex } from '~/models'
import { usePageSongKhoe } from '~/composables/page/song-khoe'
import { showError, useI18n, navigateTo } from '#imports'

const { t } = useI18n()
const switchLocalePath = useSwitchLocalePath()

const {
  hit: category,
  status,
  loading: categoryLoading,
} = useFetchElasticWithDefault(ElasticIndex.NOTION_WEBSITE, {
  filters: [{ term: { 'page.properties.Slug.keyword': 'song-khoe' } }],
})

watch(
  [categoryLoading, category],
  async () => {
    if (!categoryLoading.value && !category.value) {
      await navigateTo(switchLocalePath('vi'))
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

usePageArticle({
  hit: category,
  status: status,
})
const { topics, articles, loading }: any = usePageSongKhoe()

// Watch for loading state and handle errors
watch(
  [loading, topics],
  () => {
    if (!loading.value && !topics.value) {
      showError({
        statusCode: 404,
        statusMessage: t('error:page-not-found:message'),
        cause: t('error:page-not-found:cause'),
        fatal: true,
      })
    }
  },
  { immediate: true },
)
useSeoMeta({
  title: computed(() => `${category.value?.page?.properties?.Name} - Wellcare`),
  ogTitle: computed(
    () => `${category.value?.page?.properties?.Name} - Wellcare`,
  ),
  description: computed(
    () => `${category.value?.page?.properties?.Sapo}` || '',
  ),
  ogDescription: computed(
    () => `${category.value?.page?.properties?.Sapo}` || '',
  ),
  ogImage: computed(() => `${category.value?.page.cover?.url}` || ''),
})
</script>

<template>
  <div>
    <div
      v-if="loading || !topics || !articles"
      class="mx-auto mb-10 max-w-7xl"
    >
      <WSkeleton block="article" />
    </div>
    <div v-else>
      <WHeroSection
        class="h-96"
        :blocks="['overlay', 'title', 'sub-title', 'background-image']"
        :title="category?.page.properties.Name"
        :sub-title="category?.page.properties.Sapo"
        seo-title-tag="h1"
        :background-image="{
          src: category?.page.cover?.url,
          alt: category?.page.properties.Name,
        }"
      />
      <div class="flex flex-col px-8 md:px-24 lg:px-32 pb-10 py-8 gap-2">
        <div class="grid md:grid-flow-col gap-4">
          <div
            v-for="(topic, index) in topics?.hits"
            :key="index"
            class="rounded-xl relative overflow-hidden w-full md:w-auto h-40 md:h-52"
          >
            <template v-if="!loading">
              <NuxtImg
                :src="topic.page.cover?.url"
                width="480"
                format="webp"
                sizes="sm:320px lg:480px"
                preset="thumbnail"
                loading="lazy"
                class="static block h-full object-cover"
              />
              <div
                class="absolute left-0 top-0 flex h-full w-full flex-col justify-between bg-gradient-to-b from-black to-30% p-2"
              >
                <h4 class="text-md font-semibold text-white md:text-lg">
                  {{ topic.page.properties.Name }}
                </h4>
                <NuxtLink
                  class="ml-auto"
                  :to="`/${topic.page.properties.Slug}`"
                >
                  <Button
                    :label="t('btn:see-all')"
                    icon="pi pi-external-link"
                    icon-pos="right"
                  />
                </NuxtLink>
              </div>
            </template>
            <Skeleton
              v-else
              height="100%"
            />
          </div>
        </div>
        <div
          v-for="(topic, index) in articles"
          :key="index"
          class="mx-auto"
        >
          <div class="h3 flex flex-row justify-between">
            <h3
              v-if="!loading"
              class="w-3/5 text-2xl font-semibold md:text-3xl"
            >
              {{ topic.page.properties.Name }}
            </h3>
            <Skeleton
              v-else
              width="180px"
            />
            <NuxtLink
              v-if="!loading"
              :to="topic.page.properties.Slug"
            >
              <Button
                size="large"
                link
              >{{ t("btn:load-more") }} -></Button>
            </NuxtLink>
          </div>
          <div class="flex flex-row flex-wrap md:gap-4">
            <Card
              v-for="(hit, i) in topic.hits"
              :key="i"
              class="hover:text-primary dark:hover:text-primary w-80 cursor-pointer !bg-transparent"
            >
              <template #header>
                <NuxtImg
                  v-if="!loading"
                  :src="hit.page.cover?.url"
                  format="webp"
                  class="h-40 w-full rounded-lg object-cover"
                  :placeholder="`https://placehold.co/1920x1080?text=${hit.page.properties.Name.split(' ').slice(0, 3).join(' ')}...`"
                />
                <Skeleton
                  v-else
                  height="180px"
                />
              </template>
              <template #title>
                <NuxtLink
                  v-if="!loading"
                  :to="`${topic.page.properties.Slug}/${hit.page.properties.Slug}`"
                >
                  <h4 class="w-full text-wrap text-xl md:text-2xl">
                    {{ hit.page.properties.Name }}
                  </h4>
                </NuxtLink>
                <Skeleton v-else />
              </template>
              <template #subtitle>
                {{ hit.page.properties.Sapo }}
              </template>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.h3 {
  margin-top: 56px;
  margin-bottom: 8px;
}
</style>
