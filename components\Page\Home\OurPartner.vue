<script setup lang="ts">
const { display } = useDisplay()

const { t } = useI18n({ useScope: 'local' })

const { sprites } = useImageSprite(
  'https://storage.googleapis.com/cms-gallery/6645b58bfb2ed0521c5fa708/loogo-doi-tac-24x.png',
  { rows: 13, cols: 1 },
  { width: 0.5 * 480, height: 0.5 * 2080 },
)

const isMobile = computed(() => display.breakpoint.isMobile)
</script>

<template>
  <div class="h-fit pb-4 pt-8 md:pb-10 md:pt-20">
    <div class="mx-auto max-w-screen-lg text-center lg:mb-12">
      <h2>
        {{ t('partner:title') }}
      </h2>
      <h5>
        {{ t('partner:subtitle') }}
      </h5>
    </div>
    <nuxt-marquee
      class="mb-10 mt-6 lg:mt-0"
      :speed="60"
      :pause-on-hover="!isMobile"
      :pause-on-click="!isMobile"
    >
      <div
        v-for="(sprite, index) in sprites"
        :key="index"
        :style="sprite.style"
        class="grayscale hover:cursor-pointer hover:rounded-md hover:bg-slate-100 hover:grayscale-0 dark:invert dark:hover:invert-0"
      ></div>
    </nuxt-marquee>
  </div>
</template>

<i18n lang="yaml">
en:
  'partner:title': 'Clients and Partners'
  'partner:subtitle': 'Proud to work with leading brands'
vi:
  'partner:title': 'Khách hàng và Đối tác'
  'partner:subtitle': 'Tự hào hợp tác với thương hiệu dẫn đầu'
</i18n>
