import { use, type ComposeOption } from 'echarts/core'

import {
  <PERSON><PERSON><PERSON>,
  type <PERSON><PERSON>eriesOption,
  <PERSON><PERSON>hart,
  type Bar<PERSON>eriesOption,
  EffectScatter<PERSON><PERSON>,
  type EffectScatterSeriesOption,
  Scatter<PERSON><PERSON>,
  type ScatterSeriesOption,
  Pie<PERSON><PERSON>,
  type PieSeriesOption,
  <PERSON><PERSON>hart,
  type RadarSeriesOption,
} from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import {
  DataZoomComponent,
  type DataZoomComponentOption,
  LegendComponent,
  type LegendComponentOption,
  TooltipComponent,
  type TooltipComponentOption,
  ToolboxComponent,
  type ToolboxComponentOption,
  GridComponent,
  type GridComponentOption,
  TitleComponent,
  type TitleComponentOption,
  MarkPointComponent,
  type MarkPointComponentOption,
  DatasetComponent,
  type DatasetComponentOption,
  VisualMapComponent,
  type VisualMapComponentOption,
} from 'echarts/components'

use([
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Can<PERSON><PERSON><PERSON><PERSON>,
  Data<PERSON><PERSON><PERSON>omponent,
  Legend<PERSON>omponent,
  Toolt<PERSON><PERSON>omponent,
  Too<PERSON><PERSON>Component,
  Grid<PERSON>omponent,
  TitleComponent,
  Mark<PERSON><PERSON>Component,
  DatasetComponent,
  VisualMapComponent,
])

export type ECOption = ComposeOption<
  | LineSeriesOption
  | BarSeriesOption
  | PieSeriesOption
  | RadarSeriesOption
  | EffectScatterSeriesOption
  | ScatterSeriesOption
  | DataZoomComponentOption
  | LegendComponentOption
  | TooltipComponentOption
  | ToolboxComponentOption
  | GridComponentOption
  | TitleComponentOption
  | MarkPointComponentOption
  | DatasetComponentOption
  | VisualMapComponentOption
>

export default defineNuxtPlugin(() => {})
