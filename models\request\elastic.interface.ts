export enum ElasticIndex {
  BROADCAST_HEALTH_PROGRAM = '/elastic-read/search/broadcast-health-program/_search',
  CATALOG_PRODUCT = '/elastic-read/search/catalog_product/_search',
  NOTION_ASK_DOCTOR = '/elastic-read/search/notion-ask-doctor/_search',
  NOTION_BANNERS = '/elastic-read/search/notion-banners/_search',
  NOTION_CARETEAM_KNOWLEDGE = '/elastic-read/search/notion-careteam-knowledge/_search',
  NOTION_CONDITIONS = '/elastic-read/search/notion-conditions/_search',
  NOTION_CONTENT_HEALTH_PROGRAM = '/elastic-read/search/notion-content-health-program/_search',
  NOTION_EDU_HUB = '/elastic-read/search/notion-edu-hub/_search',
  NOTION_RATINGS = '/elastic-read/search/notion-ratings/_search',
  NOTION_SPECIALTIES = '/elastic-read/search/notion-specialties/_search',
  NOTION_WEBSITE = '/elastic-read/search/notion-website/_search',
  PROVIDERS = '/elastic-read/search/providers/_search',
  RATINGS_HOMEPAGE = '/elastic-read/search/ratings-homepage/_search',
}

export interface IHighlight {
  boundary_chars?: string
  boundary_max_scan?: number
  fields?: {
    [key: string]: IHighlight
  }
  fragment_size?: number
  number_of_fragments?: number
  pre_tags?: Array<string>
  post_tags?: Array<string>
  require_field_match?: boolean
}

export interface IFilter {
  term?: Record<string, any>
  terms?: Record<string, any[]>
  range?: Record<string, any>
  bool?: Record<string, any>
}

export interface IMultiMatch {
  query: string
  fields: string[]
}

export interface IMust {
  multi_match?: IMultiMatch
  term?: Record<string, any>
}

export interface IShould {
  multi_match?: IMultiMatch
  term?: Record<string, any>
  wildcard?: Record<string, any>
  match?: Record<string, any>
}

type TSortOrder = 'desc' | 'asc'
export interface ISortObject {
  [key: string]: TSortOrder
}
export type TSort = string | ISortObject

export interface ISource {
  excludes?: string[]
  includes?: string[]
}

interface IBool {
  must?: IMust[]
  filter?: IFilter[]
  should?: IMust[]
}
export interface IFunctionScore {
  query?: { bool: IBool }
  functions?: any[]
  boost_mode?: 'multiply' | 'replace' | 'sum' | 'avg' | 'max' | 'min'
  score_mode?: 'multiply' | 'sum' | 'avg' | 'first' | 'max' | 'min'
}

export interface RequestElasticSearch {
  from?: number
  size?: number
  sort?: TSort[]
  query?: {
    function_score?: IFunctionScore
    bool?: IBool
    must?: IMust[]
    filter?: IFilter[]
    should?: IMust[]
    minimum_should_match?: number
  }
  highlight?: IHighlight
  _source?: ISource
}
