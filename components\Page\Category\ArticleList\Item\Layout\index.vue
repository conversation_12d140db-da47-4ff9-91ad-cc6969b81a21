<script setup lang="ts">
import { defineAsyncComponent } from 'vue'
import type { Hit, NotionPage } from '~/models'

const asyncComponents = {
  gallery: defineAsyncComponent(() => import('./gallery.vue')),
  list: defineAsyncComponent(() => import('./list.vue')),
}
type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
type LayoutType = 'list' | 'grid' | 'timeline' | 'gallery'
const { article, blocks } = defineProps({
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['title', 'sapo'],
  },
  article: {
    type: Object as PropType<Hit<NotionPage>>,
    required: true,
  },
  slug: {
    type: String,
    default: '',
  },
  layout: {
    type: String as PropType<LayoutType>,
    default: () => 'list',
  },
})
</script>

<template>
  <suspense>
    <component
      :is="asyncComponents[layout]"
      :article="article"
      :slug="slug"
      :blocks="blocks"
    />
  </suspense>
</template>
