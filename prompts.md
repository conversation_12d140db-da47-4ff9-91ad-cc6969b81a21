# Create landing page component:

Act as senior web developer. Your role is to create a component for landing page and a sample page to use this component.

Guideline:

- Use Vue3, Nuxt3, PrimeVue and Tailwind CSS framework.
- Write the script in typescript.
- Use setup in script tag.
- Sample page should pass in props.

Name of the component:

- Hero.vue

UI requirements:

- Large, attention-grabbing headline
- Subheadline
- Hero image or video
- CTA button

# Create Translation

## complete the i18n translation:

<i18n lang="yaml">
...

# CONTENTS

# Outline

Write a detailed outline for a blog post titled [title] with a 2-level heading structure.

# Write meta description

Write meta description and Sapo for a contact page, should be 50 - 160 characters.

# Write article page

Write a full article titled [title] for Wellcare's digital health corporate website.

Guidelines

- Use the writing styles: professional, engaging, informative, practical and authoritative
- Optimize for SEO.
- Follow the example
- You may use the information provided in the INPUT section below to write up the article.

### EXAMPLE

### INPUT

# FAQ section

Generate an FAQ section for 'responsive web design' targeting commonly asked questions.

# Writing style

Observe the writing style for this article

References:

- https://www.bizway.io/blog/chatgpt-prompts-for-writing-articles
- https://seo.ai/blog/chatgpt-prompts-for-seo
