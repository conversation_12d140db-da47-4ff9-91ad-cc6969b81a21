<script setup lang="ts">
const props = defineProps({
  width: {
    type: Number,
    default: 0,
  },
  height: {
    type: Number,
    default: 0,
  },
  platformClass: {
    type: String,
    default: '',
  },
  platform: {
    type: String,
    default: 'Unknown',
  },
})

const { sprites } = useImageSprite(
  'https://storage.googleapis.com/cms-gallery/664c51d7f9abc41db2ea5dec/121.png',
  { rows: 2, cols: 1 },
  { width: props.width, height: props.height },
)

const shouldRenderPlayStore = computed(() =>
  ['Unknown', 'Android', 'Windows'].includes(props.platform),
)
const shouldRenderAppStore = computed(() =>
  ['Unknown', 'iOS', 'Windows', 'Mac'].includes(props.platform),
)
</script>

<template>
  <div>
    <NuxtLink
      v-if="shouldRenderPlayStore"
      :class="platformClass"
      :style="sprites[0].style"
      to="https://play.google.com/store/apps/details?id=vn.wellcare"
    />
    <NuxtLink
      v-if="shouldRenderAppStore"
      :class="platformClass"
      :style="sprites[1].style"
      to="https://apps.apple.com/us/app/wellcare/id1039423586"
    />
  </div>
</template>
