import type { Form } from '@wellcare/nuxt3-module-form/dist/runtime/models'

export function useFetchForms(query: { slug: string }) {
  const forms = ref<Form[]>([
    {
      slug: 'book-appointment',
      title: 'title',
      route: '/',
      nodes: [
        {
          id: '1',
          labels: ['input'],
          properties: {
            input: 'phone',
            // required: true,
            label: 'input:1:label',
            key: 'phone',
            placeholder: 'input.1.placeholder',
            prefix: '(+84) ',
          },
        },
        {
          id: '2',
          labels: ['input'],
          properties: {
            input: 'radio-button',
            label: 'input.2.label',
            // required: true,
            key: 'specialty',
            class: '',
            choices: [
              {
                label: 'input.2.choice.1.label',
                value: 'kids',
              },
              {
                label: 'input.2.choice.2.label',
                value: 'adults',
              },
              {
                label: 'input.2.choice.3.label',
                value: 'mental',
              },
              {
                label: 'input.2.choice.4.label',
                value: 'others',
              },
            ],
            default: 'kids',
          },
        },
        {
          id: '4',
          labels: ['input'],
          properties: {
            input: 'dropdown',
            label: 'input.4.label',
            // required: true,
            key: 'doctor',
            choices: [
              {
                label: 'Trần Thị Hồng an',
                value: 'tran-thi-hong-an',
              },
              {
                label: 'Tô Viết Thuấn',
                value: 'to-viet-thuan',
              },
              {
                label: 'Nguyễn Cảnh Nam',
                value: 'nguyen-canh-nam',
              },
              {
                label: 'Mai Duy <PERSON>h',
                value: 'mai-duy-linh',
              },
              {
                label: 'Đỗ Thị Nam Phương',
                value: 'do-thi-nam-phuong',
              },
              {
                label: 'Nguyễn Công Viên',
                value: 'nguyen-cong-vien',
              },
              {
                label: 'Đoàn Lê Minh Hạnh',
                value: 'doan-le-minh-hanh',
              },
            ],
            default: 'tran-thi-hong-an',
          },
        },
        {
          id: '3',
          labels: ['input'],
          properties: {
            input: 'dropdown',
            label: 'input.3.label',
            // required: true,
            key: 'doctor',
            class: '',
            choices: [
              {
                label: 'Nguyễn Trí Đoàn',
                value: 'nguyen-tri-doan',
              },
              {
                label: 'Lê Thúy Anh',
                value: 'le-thuy-anh',
              },
              {
                label: 'Phạm Thị Xuân Linh',
                value: 'pham-thi-xuan-linh',
              },
              {
                label: 'Võ Thị Hồng Nhị',
                value: 'vo-thi-hong-nhi',
              },
              {
                label: 'Trần Đỗ Lợi',
                value: 'tran-do-loi',
              },
              {
                label: 'Vũ Đỗ Uyên Vy',
                value: 'vu-do-uyen-vy',
              },
              {
                label: 'Nguyên Thị Kim Anh',
                value: 'nguyen-thi-kim-anh',
              },
            ],
            default: 'le-thuy-anh',
          },
        },
        {
          id: '5',
          labels: ['input'],
          properties: {
            input: 'dropdown',
            label: 'input.5.label',
            // required: true,
            key: 'doctor',
            class: '',
            choices: [
              {
                label: 'Ái Ngọc Phân',
                value: 'ai-ngoc-phan',
              },
              {
                label: 'Đặng Khánh An',
                value: 'dang-khanh-an',
              },
              {
                label: 'Đỗ Thanh Thuy',
                value: 'do-thanh-thuy',
              },
              {
                label: 'Phạm Tiến Dũng',
                value: 'pham-tien-dung',
              },
              {
                label: 'Huỳnh Thanh Tân',
                value: 'huynh-thanh-tan',
              },
              {
                label: 'Lê Ngọc Phương Uyên',
                value: 'le-ngoc-phuong-uyen',
              },
              {
                label: 'Lê Ngọc Phương Uyên',
                value: 'le-ngoc-phuong-uyen',
              },
              {
                label: 'Lê Thị Toàn',
                value: 'le-thi-toan',
              },
            ],
            default: 'ai-ngoc-phan',
          },
        },
        {
          id: '6',
          labels: ['input'],
          properties: {
            input: 'set',
            key: 'redirect',
            command:
              'return btoa(encodeURI(`https://khamtuxa.vn/checkout/${state.doctor}`))',
          },
        },
        {
          id: '7',
          labels: ['input'],
          properties: {
            input: 'set',
            key: 'phoneNumber',
            command: 'return \'84\' + (state[\'phone\'] || \'\')',
          },
        },
        {
          id: '8',
          labels: ['end'],
          properties: {
            input: 'navigate',
            key: 'navigate',
            default: 'https://account.wellcare.vn/auth/sign-in/check-phone',
          },
        },
      ],
      edges: [
        {
          from: '1',
          to: '2',
          weight: '1',
          type: 'NEXT',
        },
        {
          from: '2',
          to: '3',
          weight: 'return state[\'specialty\'] == \'kids\' ? 2 : 1',
          type: 'NEXT',
        },
        {
          from: '2',
          to: '4',
          weight: 'return state[\'specialty\'] == \'adults\' ? 2 : 1',
          type: 'NEXT',
        },
        {
          from: '2',
          to: '5',
          weight: 'return state[\'specialty\'] == \'mental\' ? 2 : 1',
          type: 'NEXT',
        },
        {
          from: '2',
          to: '6',
          weight: 'return state[\'specialty\'] == \'others\' ? 2 : 1',
          type: 'NEXT',
        },
        {
          from: '3',
          to: '6',
          weight: '1',
          type: 'NEXT',
        },
        {
          from: '4',
          to: '6',
          weight: '1',
          type: 'NEXT',
        },
        {
          from: '5',
          to: '6',
          weight: '1',
          type: 'NEXT',
        },
        {
          from: '6',
          to: '7',
          weight: '1',
          type: 'NEXT',
        },
        {
          from: '7',
          to: '8',
          weight: '1',
          type: 'COMPLETE',
        },
      ],
      locale: {
        vi: {
          'title': 'Đăng ký Khám từ xa',
          'input.1.placeholder':
            'Nhập số điện thoại bạn sẽ kết nối với chuyên gia',
          'input:1:label': 'Số điện thoại',
          'input.2.label': 'Chọn chuyên khoa:',
          'input.2.choice.1.label': 'Nội tổng quát nhi',
          'input.2.choice.2.label': 'Nội tổng quát',
          'input.2.choice.3.label': 'Tâm lý - Tâm thần học',
          'input.2.choice.4.label': 'Các chuyên khoa khác',
          'input.3.label': 'Chọn bác sĩ nhi',
          'input.4.label': 'Chọn bác sĩ',
          'input.5.label': 'Chọn chuyên gia',
        },
        en: {
          'title': 'Talk To Our Doctors Now',
          'input.1.placeholder': 'Enter the phone that you will make call',
          'input:1:label': 'Your phone',
          'input.2.label': 'Which specialty?',
          'input.2.choice.1.label': 'Kids',
          'input.2.choice.2.label': 'Adults',
          'input.2.choice.3.label': 'Mental Health',
          'input.2.choice.4.label': 'Other Specialties',
          'input.3.label': 'Choose a pediatrician',
          'input.4.label': 'Choose a GP doctor',
          'input.5.label': 'Choose a therapist',
        },
      },
      submission: 'local',
    },
    {
      slug: 'ask-question',
      title: 'title',
      route: '/',
      submission: 'local',
      nodes: [
        {
          id: '1',
          labels: ['input'],
          properties: {
            input: 'phone',
            // required: true,
            label: 'input:0:label',
            key: 'phone',
            placeholder: 'input:0:placeholder',
            prefix: '(+84) ',
          },
        },
        {
          id: '2',
          labels: ['input'],
          properties: {
            input: 'textarea',
            // required: true,
            label: 'input:1:label',
            // maxLength: 250,
            // minLength: 25,
            key: 'chiefComplaint',
            placeholder: 'input:1:placeholder',
          },
        },
        {
          id: '3',
          labels: ['input'],
          properties: {
            input: 'set',
            key: 'redirect',
            command:
              'return btoa(encodeURI(`https://khamtuxa.vn/checkout/dat-cau-hoi?chiefComplaint=${state.chiefComplaint}`))',
          },
        },
        {
          id: '7',
          labels: ['input'],
          properties: {
            input: 'set',
            key: 'phoneNumber',
            command: 'return \'84\' + (state[\'phone\'] || \'\')',
          },
        },
        {
          id: '4',
          labels: ['end'],
          properties: {
            input: 'navigate',
            key: 'navigate',
            default: 'https://account.wellcare.vn/auth/sign-in/check-phone',
          },
        },
      ],
      edges: [
        { from: '1', to: '2', weight: '1', type: 'NEXT' },
        { from: '2', to: '3', weight: '1', type: 'NEXT' },
        { from: '3', to: '4', weight: '1', type: 'COMPLETE' },
      ],
      locale: {
        vi: {
          'title': 'Hỏi Bác Sĩ',
          'input:0:placeholder': 'Nhập số điện thoại',
          'input:0:label': 'Số điện thoại',
          'input:1:label': 'Câu hỏi của bạn',
          'input:1:placeholder': 'Tối đa 250 ký tự',
        },
        en: {
          'title': 'Ask Doctor',
          'input:0:placeholder': 'Enter the phone receiving answer',
          'input:0:label': 'Your phone',
          'input:1:label': 'Your question',
          'input:1:placeholder': 'Max 250 words',
        },
      },
    },
    {
      slug: 'knowledge-question',
      title: 'title',
      route: '/',
      submission: 'local',
      workspace: '667b6ebb5cc0aa44dde98bbb',
      // theme: {
      //   name: 'textarea',
      //   form: {
      //     description: 'Description',
      //     background: {},
      //   },
      //   input: {
      //     default: {
      //       container: 'flex flex-col gap-2',
      //       label: 'w-full bg-red-500',
      //       input: 'h-40',
      //       description: 'text-sm text-blue-700 dark:text-blue-400',
      //       error: 'text-red-600 dark:text-red-400',
      //     },
      //   },
      // },
      nodes: [
        {
          id: '1',
          labels: ['input'],
          properties: {
            input: 'phone',
            label: 'i:0:label',
            key: 'phone',
            placeholder: 'i:0:placeholder',
            prefix: '(+84) ',
            validations: [
              {
                rule: 'required',
                message: 'i:0:message',
              },
            ],
          },
        },
        {
          id: '2',
          labels: ['input'],
          properties: {
            input: 'textarea',
            label: 'i:1:label',
            key: 'chiefComplaint',
            placeholder: 'i:1:placeholder',
            validations: [
              {
                rule: 'required',
                message: 'i:1:message',
              },
              {
                rule: 'maxLength',
                params: [250],
                message: 'i:1:maxLengthMessage',
              },
            ],
          },
        },
        {
          id: '3',
          labels: ['input'],
          properties: {
            input: 'set',
            key: 'redirect',
            command:
              'return btoa(encodeURI(`https://khamtuxa.vn/checkout/dat-cau-hoi?chiefComplaint=${state.chiefComplaint}`))',
          },
        },
        {
          id: '7',
          labels: ['input'],
          properties: {
            input: 'set',
            key: 'phoneNumber',
            command: 'return \'84\' + (state[\'phone\'] || \'\')',
          },
        },
        {
          id: '4',
          labels: ['end'],
          properties: {
            input: 'navigate',
            key: 'navigate',
            default: 'https://account.wellcare.vn/auth/sign-in/check-phone',
          },
        },
      ],
      edges: [
        { from: '1', to: '2', weight: '1', type: 'NEXT' },
        { from: '2', to: '3', weight: '1', type: 'NEXT' },
        { from: '3', to: '4', weight: '1', type: 'COMPLETE' },
      ],
      locale: {
        vi: {
          'title': 'Hỏi Kiến Thức',
          'i:0:placeholder': 'Nhập số điện thoại',
          'i:0:label': 'Số điện thoại',
          'i:0:message': 'Trường này là bắt buộc',
          'i:1:label': 'Câu hỏi của bạn',
          'i:1:placeholder':
            'Khác với Khám từ xa, Câu hỏi kiến thức chỉ giải đáp nhanh các thắc mắc đơn giản. Bạn viết tối đa 250 ký tự, tiếng Việt có dấu, không viết tắt hoặc dùng tiếng lóng.',
          'i:1:message': 'Trường này là bắt buộc',
          'i:1:maxLengthMessage': 'Vui lòng nhập tối đa 250 ký tự.',
        },
        en: {
          'title': 'Knowledge Question',
          'i:0:placeholder': 'Enter the phone receiving answer',
          'i:0:label': 'Your phone',
          'i:0:message': 'This field is required',
          'i:1:label': 'Your question',
          'i:1:placeholder':
            'unlike teleconsultation, short question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.',
          'i:1:message': 'This field is required',
          'i:1:maxLengthMessage': 'Please enter no more than 250 characters.',
        },
      },
    },
  ])
  const form = computed(() =>
    forms.value.find(form => form.slug == query.slug),
  )
  return { forms, form }
}
