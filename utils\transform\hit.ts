// utils/transform/hit.ts

import { blockstring } from './blockstring'
import { unflatten } from './unflatten'
import type {
  HitNotion,
  ResponseElasticSearch,
  TransformElasticHits,
} from '~/models'

const inner_hits = (item: HitNotion): HitNotion => {
  try {
    if (item.inner_hits && Object.keys(item.inner_hits).length > 0) {
      Object.keys(item.inner_hits).forEach((inner_hit_key) => {
        item.inner_hits[inner_hit_key].hits.hits = item.inner_hits[
          inner_hit_key
        ].hits.hits.map((i) => {
          return unflatten(i._source)
        })
      })
    }
  }
  catch (e) {
    console.error(e)
  }
  return item
}

export const hit = (value: ResponseElasticSearch<HitNotion>): HitNotion => {
  const initial = {
    page: { properties: {}, cover: {} },
  }
  let item = value?.body?.hits?.hits[0]
  item = blockstring(item)

  return {
    ...initial,
    ...item,
  }
}

export const hits = (
  value: ResponseElasticSearch<HitNotion>,
): TransformElasticHits<HitNotion> => {
  return {
    hits:
      value?.body?.hits?.hits
        .map((i: any) => blockstring(i))
        .map((i: any) => inner_hits(i)) || [],
    total: value?.body?.hits?.total?.value || 0,
  }
}
