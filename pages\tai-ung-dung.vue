<script setup lang="ts">
const { t, locale } = useI18n({ useScope: 'local' })
useSeoMeta({
  title: t('title'),
  ogTitle: t('title'),
  description: t('meta:description'),
  ogDescription: t('meta:description'),
  ogLocale: locale,
})
</script>

<template>
  <div class="flex flex-col justify-around p-4 sm:flex-row md:p-12 lg:p-24">
    <div class="sm mr-3 flex flex-col justify-center">
      <h1 class="mr-20 mt-6 sm:mr-40">
        {{ t('title') }}
      </h1>
      <h4>{{ t('meta:description') }}</h4>
      <PlatformDownload
        class="mt-5 flex flex-col items-center gap-4 sm:flex-row"
        :width="208"
        :height="128"
        platform-class="hover:shadow-2xl dark:!shadow-white/50 hover:-translate-y-2 duration-500 transition-all rounded-md"
      />
    </div>
    <div class="flex flex-col justify-center text-center">
      <h5 class="mb-2 mt-5 text-2xl font-semibold">
        {{ t('scan-qr:title') }}
      </h5>
      <Image
        class="mx-auto size-60 md:size-80"
        src="/images/qr-codes/wellcare-download.png"
        :pt="{
          image: 'rounded-lg',
        }"
      />
    </div>
  </div>
</template>

<i18n lang="yaml">
en:
  'title': 'Download App'
  'meta:description': 'Expert care at your fingertips. Download now.'
  'scan-qr:title': 'Scan with QR code'
vi:
  'title': 'Tải ứng dụng'
  'meta:description': 'Những lời khuyên sức khỏe đúng đắn chỉ cách bạn một cú chạm tay. Tải ứng dụng ngay!'
  'scan-qr:title': 'Quét mã QR code'
</i18n>
