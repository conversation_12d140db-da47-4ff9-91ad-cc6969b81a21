<script setup lang="ts">
import type { MenuItem } from '~/models'

const { setLocale, locale, locales } = useI18n()
const visible = ref(false)
const { top } = useMenu()
const { panel } = useMegaMenu(top as ComputedRef<MenuItem[]>)
type BlockType = 'Search' | 'Logo' | 'CTA'
interface Action {
  label: string
  route: string
}
const { blocks } = defineProps({
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['Search', 'Logo', 'CTA'],
  },
  cta: {
    type: Object as PropType<Action>,
    default: () => ({
      label: 'Contact',
      route: '/en/contact',
    }),
  },
})

const emit = defineEmits(['action:search'])

const onPanelClose = (event) => {
  if (event.item.route) visible.value = false
}
const route = useRoute()
watch(
  () => route.fullPath,
  () => {
    visible.value = false
  },
)
const availableLocales = computed(() => {
  return locales.value.filter(i => i.code !== locale.value)
})

const search = ref(false)
const color = useColorMode()

function toggleTheme() {
  color.preference = color.value === 'dark' ? 'light' : 'dark'
}
</script>

<template>
  <div class="sticky top-0 z-50 flex-nowrap justify-between align-middle">
    <Sidebar
      v-model:visible="visible"
      :show-close-icon="false"
      :block-scroll="true"
    >
      <PanelMenu
        :model="panel"
        title="Wellcare"
        @panel-close="onPanelClose"
      />
      <div class="flex flex-row gap-2 justify-end">
        <TopBarLangToggle size="normal" />
        <TopBarThemeToggle size="normal" />
      </div>
      <template #header>
        <WellcareBadge
          :size="10"
          class="grow"
          has-close-btn
          :blocks="[]"
          @action:close="visible = false"
        />
      </template>
    </Sidebar>
    <MegaMenu
      :pt="{
        root: 'sticky top-0 flex flex-row gap-1 items-center bg-white dark:bg-surface-900 dark:border-gray-600 z-40 h-16 sm:px-4 shadow-[0_8px_24px_rgba(149,157,165,0.2)]',
        menu: `hidden sm:flex ${search ? '!hidden' : 'flex flex-row'}`,
      }"
      :model="top"
      class="sm:pr-2"
    >
      <template #start>
        <slot
          v-if="blocks.includes('Logo')"
          name="Logo"
        >
          <div class="align-center flex justify-center">
            <nuxt-link
              to="/"
              class="flex"
            >
              <NuxtImg
                src="/images/logo.svg"
                alt="Logo"
                class="mx-2 size-10"
              />
            </nuxt-link>
          </div>
        </slot>
      </template>
      <template #end>
        <slot name="end">
          <slot
            v-if="blocks.includes('CTA') && search == false"
            name="cta"
          >
            <NuxtLinkLocale :to="cta.route">
              <Button
                :label="cta.label"
                icon="pi pi-download"
              />
            </NuxtLinkLocale>
          </slot>
          <slot
            v-if="blocks.includes('Search')"
            name="search"
          >
            <AppBarSearch
              v-if="search"
              layout="input"
              @action:search="emit('action:search')"
            />
            <Button
              text
              plain
              class="hidden sm:block"
              :icon="`${search ? 'pi pi-times' : 'pi pi-search'}`"
              aria-label="Search"
              @click="search = !search"
            />
            <Button
              text
              plain
              class="block sm:hidden"
              icon="pi pi-search"
              aria-label="Search"
              @click="emit('action:search')"
            />
            <Button
              text
              plain
              class="hidden font-semibold sm:block"
              @click="setLocale(availableLocales[0].code)"
            >
              {{ locale == "vi" ? "EN" : "VN" }}
            </Button>
            <Button
              text
              plain
              :icon="`pi ${color.value === 'dark' ? 'pi-sun' : 'pi-moon'}`"
              class="hidden sm:block"
              aria-label="Toggle Theme"
              @click="toggleTheme()"
            />
          </slot>
        </slot>
      </template>
      <template #menubutton>
        <Button
          icon="pi pi-bars"
          text
          plain
          class="order-last block sm:hidden"
          @click="visible = true"
        />
      </template>
    </MegaMenu>
    <slot name="bottom"></slot>
  </div>
</template>
