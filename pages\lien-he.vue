<script setup lang="ts">
defineI18nRoute({
  paths: {
    en: '/contact',
  },
})
const { t, locale } = useI18n({ useScope: 'local' })
useSeoMeta({
  title: t('title'),
  ogTitle: t('title'),
  description: t('description'),
  ogDescription: t('description'),
  ogLocale: locale,
})
useJsonld(() => ({
  '@context': 'https://schema.org',
  '@type': 'MedicalClinic',
  'image': [],
  'name': 'Wellcare - mHealth Technologies',
  'address': {
    '@type': 'PostalAddress',
    'streetAddress': 'LA0208 Lexington, 67 Mai Chi Tho, An Phu Ward',
    'addressLocality': 'Thu Duc City',
    'addressRegion': 'HCMC',
    'postalCode': '70000',
    'addressCountry': 'VN',
  },
  'geo': {
    '@type': 'GeoCoordinates',
    'latitude': 10.7995747,
    'longitude': 106.7451697,
  },
  'url': 'https://wellcare.vn',
  'telephone': '+84836226822',
}))
</script>

<template>
  <div class="container pb-20">
    <div
      class="mx-auto mt-24 flex max-w-3xl flex-col items-center justify-center"
    >
      <span
        class="bg-primary/20 border-primary/80 text-primary rounded-full border px-3 py-1 text-xs font-medium"
      >
        {{ t('contact:badge') }}
      </span>
      <h1 class="mt-8 text-center text-5xl font-bold">
        {{ t('title') }}
      </h1>
      <p class="mt-5 text-center text-lg text-gray-600 dark:text-gray-200">
        {{ t('subtitle') }}
      </p>
    </div>
    <div class="mt-12 xl:mt-24 max-w-full w-[1200px] mx-auto">
      <BlockContact />
    </div>

    <!-- <WFormContact
      :title="t('title')"
      :subtitle="t('subtitle')"
      :email="{
        label: t('input:email:label'),
        placeholder: t('input:email:placeholder'),
        required: true,
      }"
    /> -->
  </div>
</template>

<i18n lang="ts">
{
  "en": {
    "contact:badge": "GET IN TOUCH",
    "title": "Contact us",
    "subtitle": "Reach out for inquiries, support, or just to say hello. Your feedback matters!",
    "input:email:label": "Your email",
    "input:email:placeholder": "your-company-email.com",
    "input:phone:label": "Your phone",
    "input:phone:placeholder": "0912345678",
    "input:subject:label": "Subject",
    "input:subject:placeholder": "Let us know how we can help you",
    "input:message:label": "Your message",
    "input:message:placeholder": "Leave a comment...",
    "input:submit:label": "Send message"
  },
  "vi": {
    "contact:badge": "LIÊN HỆ",
    "title": "Liên hệ",
    "subtitle": "Kết nối với chúng tôi để đặt câu hỏi, hỗ trợ, hoặc chỉ đơn giản là gửi một lời chào!",
    "input:email:label": "Your email",
    "input:email:placeholder": "your-company-email.com",
    "input:phone:label": "Your phone",
    "input:phone:placeholder": "0912345678",
    "input:subject:label": "Subject",
    "input:subject:placeholder": "Let us know how we can help you",
    "input:message:label": "Your message",
    "input:message:placeholder": "Leave a comment...",
    "input:submit:label": "Send message"
  }
}
</i18n>
