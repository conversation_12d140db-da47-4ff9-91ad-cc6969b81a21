import { ElasticIndex } from '~/models'
import type { HitNotionWebsite } from '~/models'

export function usePageHoiBacSi(
  from: Ref<number> | ComputedRef<number>,
  size: Ref<number> | ComputedRef<number>,
  keyword: Ref<string> | ComputedRef<string>,
) {
  const computedShould = computed(() => {
    const output: any = []

    if (keyword.value) {
      output.push({
        multi_match: {
          fields: ['page.properties.Name'],
          query: keyword.value,
        },
      })
    }

    return output
  })

  const { hits, total } = useFetchElasticWithDefault<HitNotionWebsite>(
    ElasticIndex.NOTION_WEBSITE,
    {
      from,
      size,
      filters: [
        {
          term: {
            'page.properties.Locale.keyword': 'vi',
          },
        },
        {
          term: {
            'page.properties.Parent item.properties.Slug.keyword':
              'cau-hoi-duoc-giai-dap-bang-text',
          },
        },
      ],
      should: computedShould,
      _source: {
        includes: [
          'page.properties.Name',
          'page.properties.Slug',
          'page.properties.Last edited time',
        ],
      },
    },
  )

  const questions = computed(() => {
    return hits.value.map((hit: HitNotionWebsite) => {
      return {
        _id: hit._id,
        name: hit.page?.properties?.Name,
        slug: hit.page.properties.Slug,
        last_edited_time: hit.page?.properties?.['Last edited time'],
      }
    })
  })

  return { questions, total }
}
