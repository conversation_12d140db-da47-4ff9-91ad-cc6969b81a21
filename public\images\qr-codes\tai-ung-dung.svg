<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
width="1147px" height="1147px" viewBox="0 0 1147 1147" enable-background="new 0 0 1147 1147" xml:space="preserve">
<rect x="0" y="0" width="1147" height="1147" fill="rgb(255,255,255)" /><g transform="translate(62,62)"><g transform="translate(310,0) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(403,0) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(496,0) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(527,0) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(558,0) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,0) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(620,0) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(651,0) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(713,0) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(744,0) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(279,31) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(341,31) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(403,31) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,31) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(465,31) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,31) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(589,31) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(620,31) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(651,31) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(713,31) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,62) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(403,62) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(434,62) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(465,62) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,62) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(682,62) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(713,62) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(279,93) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(310,93) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,93) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(465,93) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(527,93) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(558,93) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,93) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(682,93) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(713,93) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,93) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(248,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(279,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(372,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(403,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(465,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(558,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(589,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(651,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(713,124) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(279,155) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(310,155) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(372,155) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(620,155) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(744,155) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(248,186) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(310,186) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(372,186) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(434,186) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(496,186) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(558,186) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(620,186) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(682,186) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(744,186) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(248,217) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,217) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,217) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(527,217) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(558,217) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,217) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(682,217) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(744,217) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(62,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(93,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(186,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(217,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(248,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(279,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(372,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(434,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(465,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(620,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(651,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(775,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(806,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(868,248) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(217,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(248,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(279,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(434,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(682,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(713,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(775,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(806,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(899,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(930,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(992,279) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(31,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(62,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(124,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(155,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(186,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(217,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(341,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(403,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(434,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(465,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(527,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(558,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(651,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(682,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(713,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(775,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(899,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(961,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(992,310) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(0,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(62,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(124,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(155,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(217,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(279,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(341,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(403,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(496,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(527,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(651,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(682,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(775,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(837,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(868,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(899,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(992,341) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(124,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(155,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(186,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(248,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(279,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(372,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(403,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(527,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(558,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(651,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(682,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(713,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(775,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(868,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(961,372) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(31,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(93,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(124,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(155,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(279,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(341,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(372,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(434,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(465,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(558,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(620,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(651,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(837,403) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(0,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(93,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(186,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(248,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(279,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(341,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(403,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(434,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(558,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(651,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(868,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(899,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,434) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(93,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(124,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(155,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(248,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(341,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(372,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(403,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(558,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(620,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(651,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(682,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(744,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,465) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(93,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(124,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(186,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(248,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(279,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(341,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(372,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(527,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(558,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(651,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(713,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(744,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(775,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(806,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(837,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(868,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(899,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(992,496) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(31,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(93,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(217,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(248,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(279,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(341,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(372,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(403,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(496,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(558,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(682,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(713,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(775,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(806,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(837,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(868,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(961,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(992,527) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(31,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(124,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(186,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(248,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(310,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(372,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(403,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(496,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(558,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(651,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(682,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(775,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(868,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(961,558) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(0,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(31,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(155,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(341,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(434,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(465,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(527,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(589,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(620,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(682,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(775,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(806,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(837,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(868,589) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(0,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(124,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(186,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(217,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(310,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(372,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(465,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(496,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(558,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(589,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(620,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(651,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(682,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(899,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(930,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(992,620) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(0,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(93,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(124,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(248,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(279,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(372,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(403,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(527,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(558,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(620,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(651,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(713,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(806,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(899,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(930,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(961,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(992,651) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(93,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(124,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(186,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(248,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(279,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(341,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(372,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(527,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(558,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(682,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(713,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(806,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(961,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(992,682) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(31,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(62,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(93,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(124,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(155,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(217,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(248,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(372,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(496,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(558,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(620,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(713,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(775,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(837,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(899,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(961,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(992,713) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(0,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(62,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(93,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(155,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(186,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(217,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(372,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(403,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(465,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(589,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(682,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(713,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(775,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(806,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(837,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(868,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(961,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(992,744) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(248,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(279,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(341,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(372,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(403,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(589,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(620,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(713,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(868,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(961,775) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(248,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(403,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(465,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(527,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(682,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(713,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(806,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(868,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,806) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(372,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(403,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(465,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(651,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(682,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(713,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(868,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(961,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(992,837) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(310,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(341,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(372,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(403,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(465,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(527,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(558,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(589,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(620,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(713,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(775,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(806,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(837,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(868,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(899,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(961,868) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(248,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(279,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(341,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(372,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(403,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(434,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(465,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(527,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(558,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(620,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(713,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(837,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(899,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(930,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(961,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(992,899) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(248,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(279,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(310,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(403,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(434,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(589,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(620,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(651,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(682,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(744,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M0,34.375V100h100V34.375V0H34.375"/>
</g>
</g></g><g transform="translate(775,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(806,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,34.375L65.625,0H0v100h100V34.375z"/>
</g>
</g></g><g transform="translate(899,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(930,930) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(279,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(434,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(465,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(496,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(558,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,40.625V100H0V40.625L50,0L100,40.625z"/>
</g>
</g></g><g transform="translate(620,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,59.375V0H0v59.375L50,100L100,59.375z"/>
</g>
</g></g><g transform="translate(682,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(713,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(744,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(806,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(992,961) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(341,992) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(372,992) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(527,992) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M40.625,0H100v100H40.625L0,50L40.625,0z"/>
</g>
</g></g><g transform="translate(558,992) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(682,992) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(713,992) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M100,65.625V0H0v100h65.625"/>
</g>
</g></g><g transform="translate(806,992) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M34.375,100H100V0H0v65.625"/>
</g>
</g></g><g transform="translate(837,992) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<rect width="100" height="100"/>
</g></g><g transform="translate(868,992) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<g>
	<path d="M59.375,0H0v100h59.375L100,50L59.375,0z"/>
</g>
</g></g><g transform="translate(930,992) scale(0.31,0.31)"><g transform="" style="fill: rgb(22, 22, 22);">
<polygon points="99.999,49.999 99.998,49.999 49.999,0 0,49.999 -0.001,49.999 -0.001,50 0,50 49.999,99.999 99.998,50 99.999,50 
	99.998,50 "/>
</g></g><g transform="translate(0,0) scale(2.17, 2.17)"><g transform=" translate(100, 0) scale(-1,1) " style="fill: rgb(0, 150, 136);">
<g>
	<path style="fill:none;" d="M85,66.221V33.75C85,23.411,76.414,15,65.859,15H34.14C23.586,15,15,23.411,15,33.75v51.246
		l50.855-0.016C76.414,84.98,85,76.564,85,66.221z"/>
	<path d="M100,66.221V33.75C100,15.141,84.68,0,65.859,0H34.14C15.32,0,0,15.141,0,33.75V100l65.859-0.02
		C84.68,99.98,100,84.84,100,66.221z M85,66.221c0,10.344-8.586,18.76-19.145,18.76L15,84.996V33.75C15,23.411,23.586,15,34.14,15
		h31.719C76.414,15,85,23.411,85,33.75V66.221z"/>
</g>
</g></g><g transform="translate(806,0) scale(2.17, 2.17)"><g transform="" style="fill: rgb(0, 150, 136);">
<g>
	<path style="fill:none;" d="M85,66.221V33.75C85,23.411,76.414,15,65.859,15H34.14C23.586,15,15,23.411,15,33.75v51.246
		l50.855-0.016C76.414,84.98,85,76.564,85,66.221z"/>
	<path d="M100,66.221V33.75C100,15.141,84.68,0,65.859,0H34.14C15.32,0,0,15.141,0,33.75V100l65.859-0.02
		C84.68,99.98,100,84.84,100,66.221z M85,66.221c0,10.344-8.586,18.76-19.145,18.76L15,84.996V33.75C15,23.411,23.586,15,34.14,15
		h31.719C76.414,15,85,23.411,85,33.75V66.221z"/>
</g>
</g></g><g transform="translate(0,806) scale(2.17, 2.17)"><g transform=" translate(100, 0) scale(-1,1)  translate(0,100) scale(1,-1) " style="fill: rgb(0, 150, 136);">
<g>
	<path style="fill:none;" d="M85,66.221V33.75C85,23.411,76.414,15,65.859,15H34.14C23.586,15,15,23.411,15,33.75v51.246
		l50.855-0.016C76.414,84.98,85,76.564,85,66.221z"/>
	<path d="M100,66.221V33.75C100,15.141,84.68,0,65.859,0H34.14C15.32,0,0,15.141,0,33.75V100l65.859-0.02
		C84.68,99.98,100,84.84,100,66.221z M85,66.221c0,10.344-8.586,18.76-19.145,18.76L15,84.996V33.75C15,23.411,23.586,15,34.14,15
		h31.719C76.414,15,85,23.411,85,33.75V66.221z"/>
</g>
</g></g><g transform="translate(62,62) scale(0.93, 0.93)"><g transform=" translate(100, 0) scale(-1,1) " style="fill: rgb(241, 91, 42);">
<path d="M100,72.779V27.195C100,12.203,87.604,0,72.37,0H27.63C12.397,0,0,12.203,0,27.195V100l72.37-0.042
	C87.604,99.958,100,87.771,100,72.779z"/>
</g></g><g transform="translate(868,62) scale(0.93, 0.93)"><g transform="" style="fill: rgb(241, 91, 42);">
<path d="M100,72.779V27.195C100,12.203,87.604,0,72.37,0H27.63C12.397,0,0,12.203,0,27.195V100l72.37-0.042
	C87.604,99.958,100,87.771,100,72.779z"/>
</g></g><g transform="translate(62,868) scale(0.93, 0.93)"><g transform=" translate(100, 0) scale(-1,1)  translate(0,100) scale(1,-1) " style="fill: rgb(241, 91, 42);">
<path d="M100,72.779V27.195C100,12.203,87.604,0,72.37,0H27.63C12.397,0,0,12.203,0,27.195V100l72.37-0.042
	C87.604,99.958,100,87.771,100,72.779z"/>
</g></g><g transform="translate(341,341) scale(34.1,34.1)" width="341" height="341"><svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 4000 4000" style="enable-background:new 0 0 4000 4000;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#027A6C;}
	.st1{fill:#F15B2A;}
</style>
<path class="st0" d="M2655.2,3742.81H1430.76c-175.71,0-318.13-142.43-318.13-318.13v-459.6h0.17
	c108.57,0,196.58,88.01,196.58,196.58v263.08c0,66.04,53.54,119.58,119.58,119.58h1226.38c66.01,0,119.52-53.53,119.52-119.53
	v-263.13c0-108.57,88.03-196.58,196.59-196.58h1.84v459.66C2973.28,3600.41,2830.87,3742.81,2655.2,3742.81"/>
<path class="st0" d="M1430.71,328.12h1224.72c175.61,0,317.97,142.36,317.97,317.95v459.91c-108.57,0-196.58-87.99-196.58-196.56
	V646.23c0-66.07-53.56-119.64-119.63-119.64H1430.76c-65.96,0-119.42,53.46-119.42,119.42v263.42
	c0,108.57-88.01,196.56-196.58,196.56h-2.14V646.23C1112.62,470.54,1255.03,328.12,1430.71,328.12"/>
<path class="st0" d="M3926.94,1422.35v1224.35c0,175.84-142.54,318.38-318.38,318.38h-459.34v-0.15
	c0-108.57,88.03-196.58,196.59-196.58h262.75c66.5,0,119.68-55.21,119.68-121.38V1422.35c0-66.21-53.18-119.39-119.68-119.39
	h-262.75c-108.57,0-196.59-88.02-196.59-196.58v-1.88h459.87C3784.63,1104.49,3926.94,1246.8,3926.94,1422.35"/>
<path class="st0" d="M473.66,1104.48h459.82v1.89c0,108.57-88.01,196.58-196.58,196.58H473.99c-66.09,0-119.67,53.56-119.67,119.66
	v1224.36c0,66.76,54.61,121.38,121.38,121.38h261.2c108.57,0,196.58,88.01,196.58,196.58v0.15H473.99
	c-175.74,0-318.18-142.46-318.18-318.18V1422.34C155.8,1246.81,298.11,1104.48,473.66,1104.48"/>
<path class="st1" d="M1277.97,2632.45c115.59,71.86,242.21,107.81,379.62,107.81c68.75-3.13,135.24-13.29,199.26-30.5
	c64.04-17.17,119.58-46.13,166.43-86.66c56.19,46.85,118.68,78.08,187.42,93.68c68.87,15.64,139.02,23.48,210.92,23.48
	c134.43-6.3,257.04-45.32,367.89-117.17c111.08-71.95,166.57-214.11,166.57-426.55v-567.85v-163.33h-163.44l-0.12,0.02
	c-0.05,0-0.29-0.02-0.48-0.02c-90.21,0-163.31,72.88-163.75,163.01l-0.36,0.32v633.46c0,75-21.9,129.63-65.68,164.08
	c-43.71,34.32-93.72,51.52-149.99,51.52c-56.22,0-107.04-17.2-152.34-51.52c-45.27-34.45-68-89.08-68-164.08v-65.61v-731.18h-135.5
	c-103.76,0-187.88,84.11-187.88,187.86v543.32v65.61c0,75-22.65,129.63-68,164.08c-45.31,34.32-96.02,51.52-152.38,51.52
	c-56.24,0-107.04-17.2-152.26-51.52c-45.19-34.45-68-89.08-68-164.08v-637.48c0-46.84-14.82-85.17-44.48-114.82
	c-29.7-29.76-67.98-44.49-114.9-44.49h-163.97v731.18C1104.57,2415.25,1162.48,2560.51,1277.97,2632.45"/>
</svg>
</g></g></svg>