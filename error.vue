<script setup lang="ts">
import { useI18n, useError, useRouter } from '#imports'

const { t } = useI18n({ useScope: 'local' })
const error = useError()
const router = useRouter()

function onOpenSearch() {
  router.push({
    query: {
      action: 'search',
      target: locale.value === 'vi' ? '/tim-kiem' : '/en/search',
    },
  })
  setTimeout(() => {
    const el: HTMLInputElement | null
      = document.querySelector('#search_input_el')
    if (el) el.focus()
  }, 300)
}

// const plans = [
//   {
//     name: t('plans:1:name'),
//     price: '50k',
//     per: t('plans:fee-per-use'),
//     descriptionBottom: t('plans:1:description-bottom'),
//     benefits: t('plans:1:benefits').split('.'),
//     actionName: t('plans:1:cta:name'),
//     actionRoute: '/download',
//   },
//   {
//     name: t('plans:2:name'),
//     price: '250k',
//     per: t('plans:fee-per-year'),
//     descriptionBottom: t('plans:2:description-bottom'),
//     benefits: t('plans:2:benefits').split('.'),
//     actionName: t('plans:2:cta:name'),
//     actionRoute: '/dich-vu/dang-ky-thanh-vien',
//   },
//   {
//     name: t('plans:3:name'),
//     price: t('plans:3:fee'),
//     benefits: t('plans:3:benefits').split('.'),
//     actionName: t('plans:3:cta:name'),
//     actionRoute: '/lien-he',
//   },
// ]

const { NODE_ENV } = process.env
const isShowLog = computed(() => NODE_ENV === 'development')
const errorStack = error.value.stack
  .split(/\r?\n/)
  .map(line => `<span>${line}</span>`)
  .join('<br>')
</script>

<template>
  <AppBar
    :blocks="['Logo', 'Search', 'CTA']"
    :cta="{ label: t('btn:download'), route: '/download' }"
    @action:search="onOpenSearch"
  >
    <template #bottom>
      <ProgressScroll />
    </template>
  </AppBar>
  <div class="mx-auto pb-8 dark:bg-gray-900">
    <img
      src="https://storage.googleapis.com/cms-gallery/66cea32285ced77ebd09b316/not-found.png"
      class="mx-auto w-full max-w-sm object-cover"
    />
    <WError
      :message="error.message || error.name"
      :guide="t('error:page-not-found:guide')"
      :actions="[{ label: t('btn:back-to-home'), route: '/' }]"
    />
    <h4 class="text-center">
      {{ t("recommended-for-you") }}
    </h4>

    <!-- <WPricingTable
      :blocks="['title', 'sub-title', 'description-bottom']"
      :title="t('plans:title')"
      :sub-title="t('plans:subtitle')"
      :description-bottom="t('plans:description-bottom')"
      :items="plans"
      class="md:-mt-8"
      :button-passthrough="{
        root: {
          style: 'padding: 12px 16px',
        },
      }"
      @on-click="(item) => $router.push(item.actionRoute)"
    /> -->

    <div
      v-show="isShowLog"
      class="bg-[# f5f5f5] mx-auto w-full max-w-[1000px] overflow-scroll rounded-xl border-t-[12px] border-t-red-400 p-10 leading-relaxed shadow-[0_8px_24px_rgba(149,157,165,0.2)]"
    >
      <div v-dompurify-html="errorStack"></div>
    </div>
  </div>
  <MainFooter
    brand-name="Wellcare"
    year="2015"
  />
</template>

<i18n lang="ts">
{
  "en": {
    "plans:1:benefits": "Teleconsultation. Knowledge Question. Health Record (included)",
    "plans:1:cta:name": "Download Wellcare App Now",
    "plans:1:name": "Regular user",
    "plans:1:description-bottom": "Consultation fee + 50k/booking Service fee",
    "plans:2:benefits": "Teleconsultation. Knowledge Question. Health Record (included). Second Opinion. Personal Doctor. Health Programs. HealthGPT (included). EduHub (included)",
    "plans:2:cta:name": "Become A Member Now",
    "plans:2:name": "Member",
    "plans:2:description-bottom": "Consultation fee + 0d Service fee (waived)",
    "plans:3:benefits": "Volume Discounts. Customized Benefits. App Integration and White labeling. Premium Support",
    "plans:3:cta:name": "Request A Quote",
    "plans:3:fee": "Contact",
    "plans:3:name": "Enterprise",
    "plans:fee-per-use": "đ/booking",
    "plans:fee-per-year": "đ/year",
    "plans:subtitle": "Choose what works best for you!",
    "plans:title": "Our Plans",
  },
  "vi": {
    "plans:1:benefits": "Khám từ xa. Câu hỏi kiến thức. Sổ sức khỏe cho cả gia đình (miễn phí)",
    "plans:1:cta:name": "Tải Ứng Dụng Wellcare Ngay",
    "plans:1:name": "Dịch vụ từng lần",
    "plans:1:description-bottom": "Phí khám + 50k/lần Phí dịch vụ",
    "plans:2:benefits": "Khám từ xa. Câu hỏi kiến thức. Sổ sức khỏe cho cả gia đình (miễn phí). Ý kiến độc lập. Bác sĩ riêng. Chương trình sức khỏe. HealthGPT (miễn phí). EduHub (miễn phí)",
    "plans:2:cta:name": "Đăng ký Thành viên",
    "plans:2:name": "Thành viên cả năm",
    "plans:2:description-bottom": "Phí khám + MIỄN phí dịch vụ",
    "plans:3:benefits": "Ưu đãi số lượng lớn. Quyền lợi sức khỏe thiết kế riêng. Tích hợp ứng dụng và nhãn trắng. Hotline hỗ trợ riêng",
    "plans:3:cta:name": "Yêu Cầu Gửi Báo Giá",
    "plans:3:fee": "Liên hệ",
    "plans:3:name": "Doanh nghiệp",
    "plans:fee-per-use": "đ/lần",
    "plans:fee-per-year": "đ/năm",
    "plans:subtitle": "Chọn phương án phù hợp nhất với bạn!",
    "plans:title": "Gói Dịch vụ",
  }
}
</i18n>
