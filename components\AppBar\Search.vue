<script setup lang="ts">
defineEmits(['action:search'])

const searchDialog = ref<boolean>(false)
const keyword = ref<string>('')

function onSearch() {
  if (keyword.value.trim() === '') return
  searchDialog.value = false
  navigateTo({
    path: '/tim-kiem',
    query: {
      q: keyword.value,
    },
  })
  keyword.value = ''
}
</script>

<template>
  <IconField icon-position="left">
    <InputIcon class="pi pi-search" />
    <InputText
      v-model="keyword"
      size="small"
      placeholder="Search"
      class="h-11"
      @keypress.enter="onSearch"
    />
  </IconField>
</template>
