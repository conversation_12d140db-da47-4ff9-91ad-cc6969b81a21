import { gtm } from './gtm.config'

export const runtimeConfig = (env: NodeJS.ProcessEnv) => ({
  // configs and secrets that available on server only
  session: {
    name: 'nuxt-session',
    password: '',
  },
  consola: {
    level: env.NUXT_CONSOLA_LEVEL ? Number(env.NUXT_CONSOLA_LEVEL) : undefined,
  },
  // configs that available on client side
  public: {
    'appEnv': env.NUXT_PUBLIC_APP_ENV,
    'nuxt3-module-data-layer': {
      baseUrl: env.NUXT_PUBLIC_NUXT3_MODULE_DATA_LAYER_BASE_URL || '',
      xTenantId: env.NUXT_PUBLIC_NUXT3_MODULE_DATA_LAYER_X_TENANT_ID || '',
      accountBaseUrl: env.NUXT_PUBLIC_NUXT3_MODULE_DATA_LAYER_ACCOUNT_BASE_URL || '',
      allowedPaths: env.NUXT_PUBLIC_ALLOWED_PATHS
        ? env.NUXT_PUBLIC_ALLOWED_PATHS.split(',')
        : ['/'],
    },
    'page': {
      status: env.NUXT_PUBLIC_PAGE_STATUS,
      site: env.NUXT_PUBLIC_PAGE_SITE,
    },
    'editor': {
      enabled: env.NUXT_PUBLIC_EDITOR_ENABLED,
    },
    gtm,
    'auth': {
      redirectUri: env.NUXT_PUBLIC_AUTH_REDIRECT_URI,
      keycloakClientId: env.NUXT_PUBLIC_AUTH_KEYCLOAK_CLIENT_ID,
      keycloakIssuer: env.NUXT_PUBLIC_AUTH_KEYCLOAK_ISSUER,
      secret: env.NUXT_PUBLIC_AUTH_SECRET,
    },
  },
})
