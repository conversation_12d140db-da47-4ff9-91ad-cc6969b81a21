import { fileURLToPath } from 'node:url'
import { describe } from 'vitest'
import { setup } from '@nuxt/test-utils/e2e'
import {
  // sitemap,
  // home,
  // robots,
  // timKiem,
  article,
  // landing,
  // category,
} from './pages'

await setup({
  rootDir: fileURLToPath(new URL('../', import.meta.url)),
  nuxtConfig: {
    ssr: true,
  },
})

describe('pages/{article}', article)
// describe('pages/{category}', category)
// describe('pages/{landing}', landing)
// describe('pages/index', home)
// describe('pages/tim-kiem', timKiem)
// describe('robots.txt', robots)
// describe('sitemap.xml', sitemap)
