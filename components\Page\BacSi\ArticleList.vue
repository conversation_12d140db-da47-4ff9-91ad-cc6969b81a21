<script setup lang="ts">
interface Props {
  articles: any[]
}
defineProps<Props>()
const dayjs = useDayjs()
</script>

<template>
  <div class="grid grid-cols-1 gap-x-16 lg:grid-cols-2">
    <transition-group>
      <Card
        v-for="(item, index) in articles"
        :key="index"
        :pt="{ body: 'p-0' }"
        class="mb-4 shrink-0 grow-0"
      >
        <template #title>
          <h6
            class="hover:text-primary cursor-pointer decoration-2 hover:underline"
          >
            {{ item.page.properties.Name }}
          </h6>
        </template>
        <template #subtitle>
          <div
            class="flex flex-wrap items-center justify-start gap-4 align-middle sm:flex-row"
          >
            <Tag
              :value="item.page.properties['Parent item'][0].properties.Name"
              class="!bg-primary/20 !text-primary"
            />
            <i class="pi pi-circle-fill text-[0.3rem]"></i>
            {{ dayjs(item.page.properties['Last edited time']).format('LL') }}
          </div>
        </template>
        <template #content>
          <p class="line-clamp-2">
            {{ item.page.properties.Sapo }}
          </p>
        </template>
      </Card>
    </transition-group>
  </div>
</template>

<style>
.v-enter-from {
  opacity: 0;
  translate: 50px 0;
}
.v-enter-to {
  opacity: 1;
  translate: 0 0;
}
.v-enter-active {
  transition: all 0.7s ease-out;
}
</style>
