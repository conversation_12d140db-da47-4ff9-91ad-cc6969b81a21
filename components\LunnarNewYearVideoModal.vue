<template>
  <div
    v-if="isOpen"
    class="modal-overlay"
    @click.self="close"
  >
    <div class="modal-content">
      <div>
        <div class="flex justify-end mb-2 text-lg">
          <button
            class="float-right text-white"
            @click="emit('close')"
          >
            [{{ t("close") }}]
          </button>
        </div>
        <video
          class="video-content"
          poster="https://storage.googleapis.com/cms-gallery-sandbox/678ccbfff67f07aee865f424/thumbnail-0000000000.jpeg"
          controls
          autoplay
          loop
        >
          <source
            src="https://storage.googleapis.com/cms-gallery-sandbox/678ccbfff67f07aee865f424/cliptet.mp4"
            type="video/mp4"
          />
          Your browser does not support the video tag.
        </video>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

defineProps({
  isOpen: Boolean,
})
const emit = defineEmits(['close'])
const { t } = useI18n()
const close = () => {
  emit('close')
}
</script>

<i18n lang="json">
{
  "vi": {
    "close": "Đóng"
  },
  "en": {
    "close": "Close"
  }
}
</i18n>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}
.modal-content {
  border-radius: 5px;
  width: 50%;
  height: 500px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.video-content {
  width: 100%;
  border-radius: 5px;
}
@media (max-width: 600px) {
  .modal-content {
    width: 85%;
  }
}
</style>
