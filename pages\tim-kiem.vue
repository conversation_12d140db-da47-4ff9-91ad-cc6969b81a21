<!-- pages/tim-kiem.vue -->
<script setup lang="ts">
import { ElasticIndex } from '~/models'
import { usePageLink, navigateTo } from '#imports'

defineI18nRoute({
  paths: {
    en: '/search',
  },
})

const route = useRoute()
const router = useRouter()
const { t } = useI18n({ useScope: 'local' })

const from = ref(0)
const pageSize = ref(20)
const keyword = ref((route.query.q as string) || '')

const keySearched = ref(keyword.value)
const computedMust = computed(() => {
  const must = []

  if (keyword.value) {
    must.push({
      multi_match: {
        query: keyword.value,
        fields: ['page.properties.Name^3', 'page.properties.Sapo^2'],
      },
    })
  }

  return must
})

const { hits, total, status, loading }: any = useFetchElasticWithDefault(
  ElasticIndex.NOTION_WEBSITE,
  {
    from: from,
    size: pageSize,
    filters: [{ term: { 'page.properties.Type.keyword': 'Article' } }],
    must: computedMust as any,
    _source: {
      excludes: [
        'page.properties.Sub-item',
        'page.properties.Labels',
        'page.properties.Status',
        'page.archived',
        'page.created_time',
      ],
      includes: ['page'],
    },
    highlight: {
      fields: {
        'page.properties.Name': {
          pre_tags: ['<mark>'],
          post_tags: ['</mark>'],
        },
        'page.properties.Sapo': {
          pre_tags: ['<mark>'],
          post_tags: ['</mark>'],
        },
      },
    },
  },
)

const onSearch = () => {
  router.push({ query: { q: keyword.value } })
}

watch(status, () => {
  if (status.value == 'success') {
    keySearched.value = keyword.value
  }
})

const result = computed(() => {
  if (keySearched.value) {
    return t('result', { r: total.value, k: keySearched.value })
  }
  else {
    return t('all')
  }
})
</script>

<template>
  <!-- eslint-disable -->
  <div class="mx-auto w-full max-w-6xl">
    <h1 class="mb-8 mt-16 text-center font-bold">
      {{ t('search') }}
    </h1>
    <SearchInputText
      v-model="keyword"
      @execute="onSearch"
      class="mx-auto max-w-5xl"
    />
    <div v-if="loading">
      <Skeleton height="40px" class="mt-3 w-full" />
      <Skeleton height="150px" class="my-3 w-full" />
    </div>
    <div v-else>
      <h2
        v-dompurify-html="result"
        class="mt-6 text-center text-xl text-slate-800 sm:mx-0 md:mb-8 md:mt-12 dark:text-white"
      />

      <Card
        v-for="hit in hits"
        :key="hit._id"
        class="mx-5 my-3 text-start sm:mx-0 dark:bg-transparent"
      >
        <template #title>
          <NuxtLink :to="usePageLink(hit.page).to as any">
            <h4
              v-dompurify-html="
                hit._highlight?.page?.properties?.Name?.[0] ||
                hit.page.properties.Name
              "
              class="hover:text-primary cursor-pointer hover:underline"
            />
          </NuxtLink>
        </template>

        <template #content>
          <div
            v-dompurify-html="
              hit._highlight?.page?.properties?.Sapo?.[0] ||
              hit.page.properties.Sapo
            "
            class="line-clamp-3"
          />
        </template>
      </Card>
      <div v-show="total === 0" class="my-3 flex justify-center">
        <Button
          :label="t('return:homepage')"
          icon="pi pi-arrow-left"
          @click="navigateTo('/')"
        />
      </div>
      <PageCategoryPaginator
        :total-records="total"
        :rows="pageSize"
        @update:first="from = $event"
      />
    </div>
  </div>
</template>

<i18n lang="yaml">
vi:
  search: 'Tìm kiếm'
  result: 'Tìm thấy {r} kết quả cho từ khóa {k}'
  all: 'Tất cả bài viết'

en:
  search: 'Search'
  result: 'Found {r} result(s) for keyword {k}'
  all: 'All articles'
</i18n>
