import { ElasticIndex } from '~/models'

export function usePageSongKhoe() {
  const { data: topics, status: topicStatus } = useFetchElasticWithDefault(
    ElasticIndex.NOTION_WEBSITE,
    {
      filters: [
        {
          term: {
            'page.properties.Parent item.properties.Slug.keyword':
              'danh-cho-ban',
          },
        },
        { term: { 'page.properties.Type.keyword': 'Category' } },
      ],
    },
  )

  const { data: parents, status: parentStatus }: any = useFetchPageSubItem({
    page: {
      filters: [
        {
          term: {
            'page.properties.Parent item.properties.Slug.keyword':
              'danh-cho-ban',
          },
        },
      ],
      size: 10,
    },
    subitem: {
      _source: { includes: ['page.properties.Name', 'page.properties.Slug'] },
      size: 3,
    },
  })

  const tabs = computed(() =>
    parents.value?.hits.map((parent: any) => parent.page.properties.Name),
  )

  const articles = computed(() => {
    return parents.value?.hits
      .filter(i => i.inner_hits)
      .map(i => ({
        page: i.page,
        hits: i.inner_hits['most_recent'].hits.hits,
      }))
  })

  const status = computed(() => {
    if (topicStatus.value === parentStatus.value) return topicStatus.value
    if (topicStatus.value === 'error' || parentStatus.value === 'error')
      return 'error'
    else return 'pending'
  })
  const loading = computed(() => status.value === 'pending')

  return {
    tabs,
    topics,
    status,
    loading,
    parents,
    articles,
  }
}
