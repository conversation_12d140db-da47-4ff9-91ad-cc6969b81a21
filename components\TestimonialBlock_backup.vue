<script setup lang="ts">
import {
  type IResponsiveOption,
  useComponentResponsiveCarousel,
} from '~/composables'
import {
  ElasticIndex,
  type HitNotionRatings,
  type IFilter,
  type IRating,
} from '~/models'

const dayjs = useDayjs()

type TRatingsIndex = ElasticIndex.NOTION_RATINGS | ElasticIndex.RATINGS_HOMEPAGE

const props = defineProps({
  elasticIndex: {
    type: String as PropType<TRatingsIndex>,
    default: ElasticIndex.NOTION_RATINGS,
  },
  filters: {
    type: Array as PropType<IFilter[]>,
    default: () => [
      {
        term: {
          'page.properties.Sentiment.keyword': 'positive',
        },
      },
      {
        term: {
          'page.properties.ShowOnHomepage': true,
        },
      },
    ],
  },
  container: {
    type: Object,
    default: () => ({
      class: '',
    }),
  },
  title: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  showTime: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['on-fetched'])

const { hits, loading, execute } = useFetchElastic<HitNotionRatings>(
  props.elasticIndex,
  {
    size: 10,
    function_score: {
      query: { bool: { filter: props.filters } },
      functions: [
        {
          random_score: {
            seed: parseInt(dayjs().format('YYYYMMDD')),
            field: '_seq_no',
          },
        },
      ],
      boost_mode: 'replace',
      score_mode: 'sum',
    },
  },
)

const { width } = useWindowSize()

const commentLength = 200
const ratings: Ref<IRating[]> = ref([])

// Responsive options for carousel
const responsiveOptions = ref([
  {
    breakpoint: '1400px',
    numVisible: 2,
    numScroll: 1,
  },
  {
    breakpoint: '575px',
    numVisible: 1,
    numScroll: 1,
  },
])

const defaultResponsiveOption: IResponsiveOption = {
  numVisible: 3,
  numScroll: 1,
}

const {
  currentPage,
  prev,
  next,
  numVisible,
  numScroll,
  _length,
  getCurrentResponsiveOption,
} = useComponentResponsiveCarousel(
  responsiveOptions,
  defaultResponsiveOption,
  ratings,
  width, // Pass the width ref here
)

watch(width, () => execute())

watch(
  hits,
  () => {
    ratings.value = hits.value?.map((hit: HitNotionRatings) => ({
      _id: hit._id || '',
      authorGender: hit.page?.properties?.RatingByGender || '',
      authorName: hit.page?.properties?.RatingByName || '',
      avatar: '',

      dateCreated: hit.page?.properties?.CreatedAt?.start || '',
      fullComment: hit.page?.properties?.Comment || '',

      halfComment:
        hit.page?.properties?.Comment?.substring(0, commentLength)
        + (hit.page?.properties?.Comment?.length || 0 > commentLength
          ? '...'
          : ''),
      isOverlap: (hit.page?.properties?.Comment?.length || 0) > commentLength,
      notionLink: hit.page?.url || '',
      stars: hit.page?.properties.Rating || 0,
    }))

    emit('on-fetched', ratings.value)
  },
  { immediate: true },
)

onMounted(() => {
  execute()
  getCurrentResponsiveOption(width.value)
})
</script>

<template>
  <Skeleton
    v-if="loading"
    class="mb-2"
    height="200"
  />

  <template v-if="ratings && ratings.length > 0">
    <div :class="[`${container.class}`]">
      <h3 v-if="title">
        {{ title }}
      </h3>

      <p v-if="description">
        {{ description }}
      </p>

      <Carousel
        v-model:page="currentPage"
        :value="ratings"
        :num-visible="numVisible"
        :num-scroll="numScroll"
        :responsive-options="responsiveOptions"
        :show-navigators="false"
        :show-indicators="false"
        pt:footer:class="flex flex-col items-center gap-4 mt-2 sm:mt-10"
        pt:items-container:class="flex flex-row"
      >
        <template
          v-if="ratings?.length > 1"
          #footer
        >
          <div class="flex flex-row gap-2">
            <Button
              text
              severity="secondary"
              icon="pi pi-arrow-left"
              :disabled="currentPage == 0"
              @click="prev"
            />

            <span class="my-auto text-sm">
              {{ currentPage + 1 }} /
              {{ _length + 1 - numVisible }}
            </span>

            <Button
              text
              severity="secondary"
              icon="pi pi-arrow-right"
              :disabled="currentPage == _length - numVisible"
              @click="next"
            />
          </div>
        </template>

        <template #item="{ data }">
          <FeedbackCard
            :rating="data"
            :pt="{ item: 'mx-2' }"
          />
        </template>
      </Carousel>
    </div>
  </template>
</template>

<i18n lang="yaml">
en:
  'see-all-feedbacks': 'See 1K+ feedbacks'
vi:
  'see-all-feedbacks': 'Xem 1K+ lời bình'
</i18n>
