<script setup lang="ts">
import type PropType from 'vue'
import type { Hit, NotionPage } from '~/models'

type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
const { article, slug, blocks }: any = defineProps({
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['title', 'sapo'],
  },
  article: {
    type: Object as PropType<Hit<NotionPage>>,
    required: true,
  },
  slug: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <article
    class="flex flex-col items-center rounded-lg border border-gray-200 bg-white shadow hover:bg-gray-100 md:max-w-xl md:flex-row dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700"
  >
    <slot
      v-if="blocks.includes('thumbnail')"
      name="thumbnail"
    >
      <Image
        class="h-96 w-full rounded-t-lg object-cover md:h-auto md:w-48 md:rounded-none md:rounded-s-lg"
        :src="article.page.cover?.url"
        :alt="article.page.properties.Name"
      />
    </slot>

    <div class="flex flex-col p-4 align-top leading-normal">
      <slot
        v-if="blocks.includes('title')"
        name="title"
      >
        <NuxtLink :to="`${slug}/${article.page.properties.Slug}`">
          <h2
            class="hover:text-primary mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white"
          >
            {{ article.page.properties.Name }}
          </h2>
        </NuxtLink>
      </slot>

      <slot
        v-if="blocks.includes('sapo')"
        name="sapo"
      >
        <p class="mb-3 font-normal text-gray-700 dark:text-gray-400">
          {{ article.page.properties.Sapo }}
        </p>
      </slot>
    </div>
  </article>
</template>
