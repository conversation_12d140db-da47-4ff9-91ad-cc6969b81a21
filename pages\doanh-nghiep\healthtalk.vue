<!-- pages/doanh-nghiep/healthtalk.vue -->
<script setup lang="ts">
import { ElasticIndex } from '~/models'

const { t } = useI18n({ useScope: 'local' })
const { hit, status } = useFetchElasticWithDefault(
  ElasticIndex.NOTION_WEBSITE,
  {
    size: 1,
    filters: [{ term: { 'page.properties.Slug.keyword': 'healthtalk' } }],
  },
)
usePageArticle({
  hit: hit,
  status: status,
})
</script>

<template>
  <PageCategory
    :key="hit?._id"
    :page="hit?.page"
  >
    <template #default="{ articles, loading }">
      <div class="mt-[30px] px-3 py-4 md:px-0">
        <div
          class="mx-auto grid max-w-screen-md grid-cols-1 items-stretch gap-x-10 gap-y-4 sm:grid-cols-2"
        >
          <PageCategoryArticleList
            :articles="articles"
            :slug="hit?.page?.properties?.Slug"
            layout="gallery"
            :blocks="['thumbnail', 'title']"
            :loading="loading"
          />
        </div>
      </div>
    </template>
    <template #actions>
      <NuxtLinkLocale
        to="/lien-he"
        class="hover:text-primary my-auto"
      >
        <Button
          :label="t('title')"
          class="text-base h-12 px-6"
        />
      </NuxtLinkLocale>
    </template>
  </PageCategory>
</template>

<i18n lang="yaml">
en:
  'title': 'Contact us'
vi:
  'title': 'Liên hệ'
</i18n>
