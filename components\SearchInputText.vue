<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'

const { t } = useI18n()
const onKeydown = (event) => {
  if (event.key == 'Enter') {
    submit()
  }
}
const submit = () => {
  emit('execute')
}

const attrs = useAttrs()
const placeholder: ComputedRef<string> = computed(
  () => (attrs.placeholder as string) || t('search'),
)

const { modelValue, pt } = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  pt: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue', 'execute'])

const updateValue = (event: Event) => {
  emit('update:modelValue', (event.target as HTMLInputElement).value)
}

const debouncedUpdateValue = useDebounceFn(updateValue, 400)
</script>

<template>
  <IconField
    icon-position="right"
    :class="['mx-auto', pt.iconField]"
  >
    <InputIcon
      :class="['pi pi-search cursor-pointer', pt.inputIcon]"
      @click="submit()"
    />
    <InputText
      :value="modelValue"
      :placeholder="placeholder"
      size="large"
      :class="pt.inputText"
      @input="debouncedUpdateValue"
      @keydown="onKeydown"
    />
  </IconField>
</template>
