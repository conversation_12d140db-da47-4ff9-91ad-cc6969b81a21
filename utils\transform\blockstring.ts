// utils/transform/blockstring.ts
import type { HitNotion } from '~/models'

export const blockstring = (item: HitNotion): HitNotion => {
  try {
    if (!item.page) throw new Error('No page')
    if (item.blockstring) {
      item.page.blocks = Object.values(JSON.parse(JSON.parse(item.blockstring)))
    }
    else {
      throw new Error('blockstring is undefined')
    }
    delete item['blockstring']
  }
  catch {
    item.page = {
      ...item.page,
      properties: {},
      blocks: [],
    }
  }
  return item
}
